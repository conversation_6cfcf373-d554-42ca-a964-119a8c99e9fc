// Comprehensive Technical Analysis Testing
const baseUrl = 'http://localhost:3001/api';

// Sample OHLCV data for testing calculations
const sampleOHLCV = [
  { open: 100, high: 105, low: 98, close: 103, volume: 1000000 },
  { open: 103, high: 107, low: 101, close: 106, volume: 1200000 },
  { open: 106, high: 108, low: 104, close: 105, volume: 900000 },
  { open: 105, high: 109, low: 103, close: 108, volume: 1100000 },
  { open: 108, high: 112, low: 106, close: 110, volume: 1300000 },
  { open: 110, high: 113, low: 108, close: 109, volume: 950000 },
  { open: 109, high: 111, low: 107, close: 108, volume: 1050000 },
  { open: 108, high: 110, low: 106, close: 107, volume: 1150000 },
  { open: 107, high: 109, low: 105, close: 106, volume: 1000000 },
  { open: 106, high: 108, low: 104, close: 105, volume: 1100000 },
  { open: 105, high: 107, low: 103, close: 104, volume: 1200000 },
  { open: 104, high: 106, low: 102, close: 103, volume: 1000000 },
  { open: 103, high: 105, low: 101, close: 102, volume: 1150000 },
  { open: 102, high: 104, low: 100, close: 101, volume: 1300000 },
  { open: 101, high: 103, low: 99, close: 100, volume: 1400000 }
];

// Technical Analysis Calculation Tests
function testSMACalculation() {
  console.log('\n📊 Testing SMA Calculation');
  
  const prices = sampleOHLCV.map(d => d.close);
  const period = 5;
  
  // Calculate SMA manually for verification
  const lastFivePrices = prices.slice(-period);
  const expectedSMA = lastFivePrices.reduce((sum, price) => sum + price, 0) / period;
  
  console.log(`📈 Last ${period} prices: [${lastFivePrices.join(', ')}]`);
  console.log(`🧮 Expected SMA(${period}): ${expectedSMA.toFixed(2)}`);
  
  return expectedSMA;
}

function testRSICalculation() {
  console.log('\n📊 Testing RSI Calculation');
  
  const prices = sampleOHLCV.map(d => d.close);
  
  // Calculate price changes
  const changes = [];
  for (let i = 1; i < prices.length; i++) {
    changes.push(prices[i] - prices[i - 1]);
  }
  
  // Separate gains and losses
  const gains = changes.map(change => change > 0 ? change : 0);
  const losses = changes.map(change => change < 0 ? Math.abs(change) : 0);
  
  console.log(`📈 Price changes: [${changes.map(c => c.toFixed(2)).join(', ')}]`);
  console.log(`📊 Gains: ${gains.filter(g => g > 0).length}, Losses: ${losses.filter(l => l > 0).length}`);
  
  // Calculate average gain and loss (simplified)
  const avgGain = gains.reduce((sum, gain) => sum + gain, 0) / gains.length;
  const avgLoss = losses.reduce((sum, loss) => sum + loss, 0) / losses.length;
  
  const rs = avgGain / (avgLoss || 0.01); // Avoid division by zero
  const rsi = 100 - (100 / (1 + rs));
  
  console.log(`🧮 Expected RSI: ${rsi.toFixed(2)}`);
  
  return rsi;
}

function testBollingerBands() {
  console.log('\n📊 Testing Bollinger Bands Calculation');
  
  const prices = sampleOHLCV.map(d => d.close);
  const period = 10;
  const multiplier = 2;
  
  // Calculate SMA
  const recentPrices = prices.slice(-period);
  const sma = recentPrices.reduce((sum, price) => sum + price, 0) / period;
  
  // Calculate standard deviation
  const variance = recentPrices.reduce((sum, price) => sum + Math.pow(price - sma, 2), 0) / period;
  const stdDev = Math.sqrt(variance);
  
  const upperBand = sma + (multiplier * stdDev);
  const lowerBand = sma - (multiplier * stdDev);
  
  console.log(`📈 SMA: ${sma.toFixed(2)}`);
  console.log(`📊 Std Dev: ${stdDev.toFixed(2)}`);
  console.log(`🧮 Expected Bollinger Bands: Upper=${upperBand.toFixed(2)}, Lower=${lowerBand.toFixed(2)}`);
  
  return { upper: upperBand, middle: sma, lower: lowerBand };
}

async function testLiveTechnicalAnalysis() {
  console.log('\n🔍 Testing Live Technical Analysis');
  
  try {
    // Get a ticker with technical analysis data
    const response = await fetch(`${baseUrl}/tickers`);
    const data = await response.json();
    
    if (data.success && data.data.length > 0) {
      const ticker = data.data[0];
      console.log(`\n📊 Analyzing ${ticker.ticker}`);
      console.log(`💰 Current Price: $${ticker.currentPrice}`);
      
      // Verify technical indicators exist and are reasonable
      const indicators = ticker.technicalIndicators;
      
      console.log('\n🔬 Technical Indicators Validation:');
      
      // RSI should be between 0 and 100
      const rsiValid = indicators.rsi >= 0 && indicators.rsi <= 100;
      console.log(`📈 RSI: ${indicators.rsi.toFixed(2)} ${rsiValid ? '✅' : '❌'} (0-100 range)`);
      
      // ATR should be positive
      const atrValid = indicators.atr > 0;
      console.log(`📊 ATR: ${indicators.atr.toFixed(2)} ${atrValid ? '✅' : '❌'} (positive)`);
      
      // ADX should be between 0 and 100
      const adxValid = indicators.adx >= 0 && indicators.adx <= 100;
      console.log(`📈 ADX: ${indicators.adx.toFixed(2)} ${adxValid ? '✅' : '❌'} (0-100 range)`);
      
      // MACD values should be reasonable
      const macd = indicators.macd;
      console.log(`📊 MACD: ${macd.macd.toFixed(4)} ✅`);
      console.log(`📈 Signal: ${macd.signal.toFixed(4)} ✅`);
      console.log(`📊 Histogram: ${macd.histogram.toFixed(4)} ✅`);
      
      // Bollinger Bands should be ordered correctly
      const bb = indicators.bollingerBands;
      const bbValid = bb.lower < bb.middle && bb.middle < bb.upper;
      console.log(`📈 Bollinger Bands: L=${bb.lower.toFixed(2)}, M=${bb.middle.toFixed(2)}, U=${bb.upper.toFixed(2)} ${bbValid ? '✅' : '❌'}`);
      
      // SMA values should be reasonable
      const sma = indicators.sma;
      console.log(`📊 SMA Short: ${sma.short.toFixed(2)} ✅`);
      console.log(`📈 SMA Long: ${sma.long.toFixed(2)} ✅`);
      console.log(`🔄 Crossover: ${sma.crossover} ✅`);
      
      // Test recommendation logic
      console.log('\n🎯 Recommendation Analysis:');
      const rec = ticker.recommendation;
      console.log(`📊 Action: ${rec.action} ✅`);
      console.log(`💭 Sentiment: ${rec.sentiment} ✅`);
      console.log(`🎯 Confidence: ${rec.confidence} ✅`);
      console.log(`📝 Reasoning: ${rec.reasoning.substring(0, 50)}... ✅`);
      
      return {
        ticker: ticker.ticker,
        indicators: indicators,
        recommendation: rec,
        validations: {
          rsi: rsiValid,
          atr: atrValid,
          adx: adxValid,
          bollingerBands: bbValid
        }
      };
    } else {
      console.log('❌ No ticker data available for testing');
      return null;
    }
  } catch (error) {
    console.log(`💥 Error testing live technical analysis: ${error.message}`);
    return null;
  }
}

async function testHistoricalDataAccuracy() {
  console.log('\n📈 Testing Historical Data Accuracy');
  
  try {
    // Test with a known ticker
    const response = await fetch(`${baseUrl}/tickers/AAPL/history`);
    const data = await response.json();
    
    if (data.success && data.data.length > 0) {
      const history = data.data;
      console.log(`📊 Historical data points: ${history.length}`);
      
      // Validate data structure
      const firstPoint = history[0];
      const requiredFields = ['date', 'open', 'high', 'low', 'close', 'volume'];
      const hasAllFields = requiredFields.every(field => firstPoint.hasOwnProperty(field));
      
      console.log(`📋 Data structure: ${hasAllFields ? '✅' : '❌'}`);
      
      // Validate OHLC relationships
      let validOHLC = 0;
      let totalPoints = Math.min(history.length, 10); // Check first 10 points
      
      for (let i = 0; i < totalPoints; i++) {
        const point = history[i];
        const valid = point.low <= point.open && point.open <= point.high &&
                     point.low <= point.close && point.close <= point.high &&
                     point.low <= point.high;
        if (valid) validOHLC++;
      }
      
      console.log(`📊 OHLC validation: ${validOHLC}/${totalPoints} valid ${validOHLC === totalPoints ? '✅' : '❌'}`);
      
      // Check for reasonable price ranges
      const prices = history.slice(0, 10).map(h => h.close);
      const minPrice = Math.min(...prices);
      const maxPrice = Math.max(...prices);
      const priceRange = maxPrice - minPrice;
      const avgPrice = prices.reduce((sum, p) => sum + p, 0) / prices.length;
      
      console.log(`💰 Price range: $${minPrice.toFixed(2)} - $${maxPrice.toFixed(2)} (avg: $${avgPrice.toFixed(2)})`);
      console.log(`📊 Price volatility: ${((priceRange / avgPrice) * 100).toFixed(2)}%`);
      
      return {
        dataPoints: history.length,
        structureValid: hasAllFields,
        ohlcValid: validOHLC === totalPoints,
        priceRange: { min: minPrice, max: maxPrice, avg: avgPrice }
      };
    } else {
      console.log('❌ No historical data available');
      return null;
    }
  } catch (error) {
    console.log(`💥 Error testing historical data: ${error.message}`);
    return null;
  }
}

async function runComprehensiveTests() {
  console.log('🧪 Starting Comprehensive Technical Analysis Tests');
  console.log('=' .repeat(60));
  
  // Test 1: Manual calculation verification
  console.log('\n1️⃣ Manual Calculation Tests');
  const expectedSMA = testSMACalculation();
  const expectedRSI = testRSICalculation();
  const expectedBB = testBollingerBands();
  
  // Test 2: Live technical analysis validation
  console.log('\n2️⃣ Live Technical Analysis Tests');
  const liveAnalysis = await testLiveTechnicalAnalysis();
  
  // Test 3: Historical data accuracy
  console.log('\n3️⃣ Historical Data Accuracy Tests');
  const historicalTest = await testHistoricalDataAccuracy();
  
  // Test 4: Summary and recommendations
  console.log('\n📋 Test Summary');
  console.log('=' .repeat(40));
  
  if (liveAnalysis) {
    const validations = liveAnalysis.validations;
    const allValid = Object.values(validations).every(v => v);
    console.log(`🎯 Technical Indicators: ${allValid ? '✅ All Valid' : '❌ Some Issues'}`);
  }
  
  if (historicalTest) {
    console.log(`📈 Historical Data: ${historicalTest.structureValid && historicalTest.ohlcValid ? '✅ Valid' : '❌ Issues Found'}`);
  }
  
  console.log('\n🏁 Comprehensive Tests Complete');
  console.log('=' .repeat(60));
}

// Run the comprehensive tests
runComprehensiveTests().catch(console.error);
