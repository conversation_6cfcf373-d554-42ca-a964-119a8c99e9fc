import { TechnicalIndicators, SentimentAnalysis, TradeRecommendation, ActionType, ConfidenceType, SignalType, SentimentType } from '../types';
import { TechnicalAnalysisService } from './TechnicalAnalysisService';
import { logger } from '../utils/logger';

export class RecommendationService {
  private techAnalysisService: TechnicalAnalysisService;

  constructor() {
    this.techAnalysisService = new TechnicalAnalysisService();
  }

  /**
   * Generate a trade recommendation based on technical indicators and sentiment analysis
   */
  public generateRecommendation(
    ticker: string,
    currentPrice: number,
    indicators: TechnicalIndicators,
    sentiment: SentimentAnalysis
  ): TradeRecommendation {
    try {
      const technicalSignals = this.techAnalysisService.getTechnicalSignals(indicators);
      
      const { action, confidence, reasoning } = this.evaluateSignals(technicalSignals, sentiment);
      
      const riskManagement = this.techAnalysisService.calculateRiskManagement(currentPrice, indicators.atr);
      
      const recommendation: TradeRecommendation = {
        ticker,
        action,
        confidence,
        technicalSignals,
        sentiment: sentiment.sentiment,
        reasoning,
        riskManagement,
        timestamp: new Date()
      };

      logger.info(`Generated recommendation for ${ticker}`, { recommendation });
      return recommendation;
    } catch (error) {
      logger.error(`Error generating recommendation for ${ticker}:`, error);
      // Return a default "Hold" recommendation on error
      return this.getDefaultRecommendation(ticker, currentPrice, indicators, sentiment);
    }
  }

  /**
   * Evaluate technical and sentiment signals to determine action, confidence, and reasoning
   */
  private evaluateSignals(
    technicalSignals: { rsi: SignalType; macd: SignalType; bollingerBands: SignalType; sma: SignalType },
    sentiment: SentimentAnalysis
  ): { action: ActionType; confidence: ConfidenceType; reasoning: string } {
    const bullishSignals = Object.values(technicalSignals).filter(s => s === 'bullish').length;
    const bearishSignals = Object.values(technicalSignals).filter(s => s === 'bearish').length;
    const neutralSignals = Object.values(technicalSignals).filter(s => s === 'neutral').length;

    const sentimentScore = this.getSentimentScore(sentiment.sentiment);

    // Decision matrix with more nuanced thresholds
    let action: ActionType = 'Hold';
    let reasoning = 'Neutral signals, holding position.';

    // Strong bullish cases
    if (bullishSignals >= 3) {
      action = 'Buy';
      reasoning = `Strong bullish technical signals (${bullishSignals}/4 indicators bullish).`;
    }
    // Moderate bullish cases
    else if (bullishSignals >= 2 && sentimentScore > 0) {
      action = 'Buy';
      reasoning = `Moderate bullish technical signals (${bullishSignals}/4) with positive sentiment.`;
    }
    // Weak bullish with strong positive sentiment
    else if (bullishSignals >= 1 && sentimentScore > 0 && sentiment.confidence === 'High') {
      action = 'Buy';
      reasoning = `Bullish technical signal with strong positive sentiment.`;
    }

    // Strong bearish cases
    if (bearishSignals >= 3) {
      action = 'Sell';
      reasoning = `Strong bearish technical signals (${bearishSignals}/4 indicators bearish).`;
    }
    // Moderate bearish cases
    else if (bearishSignals >= 2 && sentimentScore < 0) {
      action = 'Sell';
      reasoning = `Moderate bearish technical signals (${bearishSignals}/4) with negative sentiment.`;
    }
    // Weak bearish with strong negative sentiment
    else if (bearishSignals >= 1 && sentimentScore < 0 && sentiment.confidence === 'High') {
      action = 'Sell';
      reasoning = `Bearish technical signal with strong negative sentiment.`;
    }

    // Confidence level
    const confidence = this.calculateConfidence(bullishSignals, bearishSignals, neutralSignals, sentiment);

    return { action, confidence, reasoning };
  }

  /**
   * Calculate the confidence level of a recommendation
   */
  private calculateConfidence(
    bullishSignals: number,
    bearishSignals: number,
    neutralSignals: number,
    sentiment: SentimentAnalysis
  ): ConfidenceType {
    const totalSignals = bullishSignals + bearishSignals + neutralSignals;
    const sentimentConfidence = sentiment.confidence === 'High' ? 1 : (sentiment.confidence === 'Medium' ? 0.5 : 0.25);

    // Calculate signal strength (how many non-neutral signals we have)
    const nonNeutralSignals = bullishSignals + bearishSignals;
    const signalStrength = nonNeutralSignals / 4; // Max 4 signals

    // Calculate signal consensus (how aligned the signals are)
    const strongestDirection = Math.max(bullishSignals, bearishSignals);
    const signalConsensus = totalSignals > 0 ? strongestDirection / totalSignals : 0;

    // Base confidence from technical signals
    let technicalConfidence = (signalStrength * 0.6) + (signalConsensus * 0.4);

    // Boost confidence with sentiment alignment
    const sentimentBoost = sentimentConfidence * 0.3;

    // Final confidence score
    const confidenceScore = (technicalConfidence * 0.7) + sentimentBoost;

    // More nuanced thresholds
    if (confidenceScore > 0.7) return 'High';
    if (confidenceScore > 0.4) return 'Medium';
    return 'Low';
  }

  /**
   * Get a numerical score for sentiment
   */
  private getSentimentScore(sentiment: SentimentType): number {
    if (sentiment === 'Positive') return 1;
    if (sentiment === 'Negative') return -1;
    return 0;
  }

  /**
   * Create a default "Hold" recommendation in case of errors
   */
  private getDefaultRecommendation(
    ticker: string,
    currentPrice: number,
    indicators: TechnicalIndicators,
    sentiment: SentimentAnalysis
  ): TradeRecommendation {
    return {
      ticker,
      action: 'Hold',
      confidence: 'Low',
      technicalSignals: this.techAnalysisService.getTechnicalSignals(indicators),
      sentiment: sentiment.sentiment,
      reasoning: 'Default to hold due to an internal error during analysis.',
      riskManagement: this.techAnalysisService.calculateRiskManagement(currentPrice, indicators.atr),
      timestamp: new Date()
    };
  }
} 