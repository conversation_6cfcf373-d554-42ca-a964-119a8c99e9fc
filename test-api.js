// Simple API testing script
const baseUrl = 'http://localhost:3001/api';

async function testEndpoint(endpoint, description) {
  try {
    console.log(`\n🧪 Testing: ${description}`);
    console.log(`📡 GET ${baseUrl}${endpoint}`);
    
    const response = await fetch(`${baseUrl}${endpoint}`);
    const data = await response.json();
    
    if (response.ok) {
      console.log(`✅ Success (${response.status})`);
      console.log(`📊 Data preview:`, JSON.stringify(data, null, 2).substring(0, 200) + '...');
    } else {
      console.log(`❌ Failed (${response.status})`);
      console.log(`🚨 Error:`, data);
    }
  } catch (error) {
    console.log(`💥 Network Error:`, error.message);
  }
}

async function runTests() {
  console.log('🚀 Starting API Integration Tests');
  console.log('=' .repeat(50));

  // Test ticker endpoints
  await testEndpoint('/tickers', 'Get all tickers');
  await testEndpoint('/tickers/dashboard/summary', 'Get dashboard summary');
  await testEndpoint('/tickers/AAPL', 'Get specific ticker (AAPL)');
  await testEndpoint('/tickers/AAPL/history', 'Get historical data (AAPL)');
  await testEndpoint('/logs', 'Get system logs');

  // Test discovery endpoints
  await testEndpoint('/discovery/stats', 'Get discovery statistics');
  await testEndpoint('/discovery/news?categories=business&hours=24&maxArticles=5', 'Get global news');
  await testEndpoint('/discovery/tickers', 'Discover new tickers');

  console.log('\n🏁 API Integration Tests Complete');
  console.log('=' .repeat(50));
}

// Run the tests
runTests().catch(console.error);
