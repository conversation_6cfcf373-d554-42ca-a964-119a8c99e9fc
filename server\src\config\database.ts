import mongoose from 'mongoose';
import { logger } from '../utils/logger';

export const connectDatabase = async (): Promise<void> => {
  try {
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/trading-bot';

    await mongoose.connect(mongoUri);

    logger.info('Connected to MongoDB successfully');

    // Handle connection events
    mongoose.connection.on('error', (error) => {
      logger.error('MongoDB connection error:', error);
    });

    mongoose.connection.on('disconnected', () => {
      logger.warn('MongoDB disconnected');
    });

  } catch (error) {
    logger.error('Failed to connect to MongoDB:', error);
    logger.warn('Running without MongoDB - some features may be limited');
    // Don't throw error in development mode to allow app to start without MongoDB
    if (process.env.NODE_ENV === 'production') {
      throw error;
    }
  }
};