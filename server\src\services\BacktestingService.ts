import { OHLCV, AdvancedTechnicalIndicators, SignalStrength } from '../types';
import { AdvancedTechnicalAnalysisService } from './AdvancedTechnicalAnalysisService';
import { logger } from '../utils/logger';

export interface BacktestTrade {
  entryDate: Date;
  exitDate: Date;
  entryPrice: number;
  exitPrice: number;
  quantity: number;
  side: 'long' | 'short';
  pnl: number;
  pnlPercent: number;
  stopLoss?: number;
  takeProfit?: number;
  exitReason: 'stop_loss' | 'take_profit' | 'signal_exit' | 'end_of_data';
  holdingPeriod: number; // in days
}

export interface BacktestResults {
  totalTrades: number;
  winningTrades: number;
  losingTrades: number;
  winRate: number;
  totalReturn: number;
  totalReturnPercent: number;
  maxDrawdown: number;
  maxDrawdownPercent: number;
  sharpeRatio: number;
  profitFactor: number;
  averageWin: number;
  averageLoss: number;
  averageHoldingPeriod: number;
  trades: BacktestTrade[];
  equityCurve: { date: Date; equity: number; drawdown: number }[];
  monthlyReturns: { month: string; return: number }[];
}

export interface BacktestConfig {
  initialCapital: number;
  positionSizing: 'fixed' | 'percent_of_equity' | 'kelly_criterion';
  positionSize: number; // dollar amount or percentage
  stopLossPercent: number;
  takeProfitPercent: number;
  commission: number; // per trade
  slippage: number; // percentage
  startDate: Date;
  endDate: Date;
  riskFreeRate: number; // for Sharpe ratio calculation
}

export class BacktestingService {
  private technicalAnalysisService: AdvancedTechnicalAnalysisService;

  constructor() {
    this.technicalAnalysisService = new AdvancedTechnicalAnalysisService();
  }

  /**
   * Run backtest on historical data
   */
  public async runBacktest(
    ohlcvData: OHLCV[],
    config: BacktestConfig,
    ticker: string
  ): Promise<BacktestResults> {
    try {
      logger.info('Starting backtest', { ticker, dataPoints: ohlcvData.length });

      const trades: BacktestTrade[] = [];
      const equityCurve: { date: Date; equity: number; drawdown: number }[] = [];
      
      let currentEquity = config.initialCapital;
      let maxEquity = config.initialCapital;
      let currentPosition: 'long' | 'short' | null = null;
      let entryPrice = 0;
      let entryDate = new Date();
      let stopLoss = 0;
      let takeProfit = 0;
      let quantity = 0;

      // Filter data by date range
      const filteredData = ohlcvData.filter(candle => 
        candle.timestamp >= config.startDate && candle.timestamp <= config.endDate
      );

      if (filteredData.length < 100) {
        throw new Error('Insufficient data for backtesting');
      }

      // Process each candle
      for (let i = 100; i < filteredData.length; i++) {
        const currentCandle = filteredData[i];
        const historicalData = filteredData.slice(0, i + 1);

        // Calculate technical indicators
        const indicators = this.technicalAnalysisService.calculateAdvancedIndicators(historicalData);
        const signalStrength = this.technicalAnalysisService.calculateSignalStrength(indicators);

        // Check for exit conditions if in position
        if (currentPosition) {
          const exitResult = this.checkExitConditions(
            currentCandle,
            currentPosition,
            stopLoss,
            takeProfit,
            signalStrength
          );

          if (exitResult.shouldExit) {
            // Close position
            const trade = this.closeTrade(
              entryDate,
              currentCandle.timestamp,
              entryPrice,
              exitResult.exitPrice,
              quantity,
              currentPosition,
              exitResult.exitReason,
              config.commission,
              config.slippage
            );

            trades.push(trade);
            currentEquity += trade.pnl;
            currentPosition = null;

            logger.debug('Trade closed', { 
              ticker, 
              pnl: trade.pnl, 
              reason: trade.exitReason 
            });
          }
        }

        // Check for entry conditions if not in position
        if (!currentPosition) {
          const entrySignal = this.getEntrySignal(signalStrength, indicators);
          
          if (entrySignal !== null) {
            // Calculate position size
            const positionValue = this.calculatePositionSize(
              currentEquity,
              config.positionSizing,
              config.positionSize,
              currentCandle.close,
              indicators.atr
            );

            quantity = Math.floor(positionValue / currentCandle.close);
            
            if (quantity > 0) {
              currentPosition = entrySignal;
              entryPrice = currentCandle.close * (1 + config.slippage / 100);
              entryDate = currentCandle.timestamp;
              
              // Set stop loss and take profit
              if (entrySignal === 'long') {
                stopLoss = entryPrice * (1 - config.stopLossPercent / 100);
                takeProfit = entryPrice * (1 + config.takeProfitPercent / 100);
              } else {
                stopLoss = entryPrice * (1 + config.stopLossPercent / 100);
                takeProfit = entryPrice * (1 - config.takeProfitPercent / 100);
              }

              logger.debug('Trade opened', { 
                ticker, 
                side: entrySignal, 
                price: entryPrice,
                quantity 
              });
            }
          }
        }

        // Update equity curve
        let unrealizedPnl = 0;
        if (currentPosition && quantity > 0) {
          if (currentPosition === 'long') {
            unrealizedPnl = (currentCandle.close - entryPrice) * quantity;
          } else {
            unrealizedPnl = (entryPrice - currentCandle.close) * quantity;
          }
        }

        const totalEquity = currentEquity + unrealizedPnl;
        maxEquity = Math.max(maxEquity, totalEquity);
        const drawdown = (maxEquity - totalEquity) / maxEquity;

        equityCurve.push({
          date: currentCandle.timestamp,
          equity: totalEquity,
          drawdown: drawdown * 100
        });
      }

      // Close any remaining position at the end
      if (currentPosition && quantity > 0) {
        const lastCandle = filteredData[filteredData.length - 1];
        const trade = this.closeTrade(
          entryDate,
          lastCandle.timestamp,
          entryPrice,
          lastCandle.close,
          quantity,
          currentPosition,
          'end_of_data',
          config.commission,
          config.slippage
        );
        trades.push(trade);
        currentEquity += trade.pnl;
      }

      // Calculate performance metrics
      const results = this.calculatePerformanceMetrics(
        trades,
        equityCurve,
        config.initialCapital,
        config.riskFreeRate
      );

      logger.info('Backtest completed', { 
        ticker, 
        totalTrades: results.totalTrades,
        winRate: results.winRate,
        totalReturn: results.totalReturnPercent
      });

      return results;
    } catch (error) {
      logger.error('Error in backtesting:', error);
      throw error;
    }
  }

  /**
   * Check exit conditions for current position
   */
  private checkExitConditions(
    candle: OHLCV,
    position: 'long' | 'short',
    stopLoss: number,
    takeProfit: number,
    signalStrength: SignalStrength
  ): { shouldExit: boolean; exitPrice: number; exitReason: BacktestTrade['exitReason'] } {
    if (position === 'long') {
      // Check stop loss
      if (candle.low <= stopLoss) {
        return { shouldExit: true, exitPrice: stopLoss, exitReason: 'stop_loss' };
      }
      
      // Check take profit
      if (candle.high >= takeProfit) {
        return { shouldExit: true, exitPrice: takeProfit, exitReason: 'take_profit' };
      }
      
      // Check signal exit (strong bearish signal)
      if (signalStrength.overall < 30) {
        return { shouldExit: true, exitPrice: candle.close, exitReason: 'signal_exit' };
      }
    } else {
      // Short position
      if (candle.high >= stopLoss) {
        return { shouldExit: true, exitPrice: stopLoss, exitReason: 'stop_loss' };
      }
      
      if (candle.low <= takeProfit) {
        return { shouldExit: true, exitPrice: takeProfit, exitReason: 'take_profit' };
      }
      
      if (signalStrength.overall > 70) {
        return { shouldExit: true, exitPrice: candle.close, exitReason: 'signal_exit' };
      }
    }

    return { shouldExit: false, exitPrice: 0, exitReason: 'signal_exit' };
  }

  /**
   * Get entry signal based on technical analysis
   */
  private getEntrySignal(
    signalStrength: SignalStrength,
    indicators: AdvancedTechnicalIndicators
  ): 'long' | 'short' | null {
    // Strong bullish signal
    if (signalStrength.overall > 75 && 
        indicators.patternRecognition.trendDirection === 'uptrend' &&
        indicators.volumeAnalysis.volumeTrend === 'increasing') {
      return 'long';
    }
    
    // Strong bearish signal
    if (signalStrength.overall < 25 && 
        indicators.patternRecognition.trendDirection === 'downtrend' &&
        indicators.volumeAnalysis.volumeTrend === 'increasing') {
      return 'short';
    }

    return null;
  }

  /**
   * Calculate position size based on strategy
   */
  private calculatePositionSize(
    equity: number,
    strategy: BacktestConfig['positionSizing'],
    size: number,
    price: number,
    atr: number
  ): number {
    switch (strategy) {
      case 'fixed':
        return size;
      
      case 'percent_of_equity':
        return equity * (size / 100);
      
      case 'kelly_criterion':
        // Simplified Kelly criterion (would need win rate and average win/loss)
        const kellyPercent = Math.min(0.25, size / 100); // Cap at 25%
        return equity * kellyPercent;
      
      default:
        return size;
    }
  }

  /**
   * Close a trade and calculate P&L
   */
  private closeTrade(
    entryDate: Date,
    exitDate: Date,
    entryPrice: number,
    exitPrice: number,
    quantity: number,
    side: 'long' | 'short',
    exitReason: BacktestTrade['exitReason'],
    commission: number,
    slippage: number
  ): BacktestTrade {
    const adjustedExitPrice = exitPrice * (1 - slippage / 100);
    
    let pnl: number;
    if (side === 'long') {
      pnl = (adjustedExitPrice - entryPrice) * quantity - (commission * 2);
    } else {
      pnl = (entryPrice - adjustedExitPrice) * quantity - (commission * 2);
    }

    const pnlPercent = (pnl / (entryPrice * quantity)) * 100;
    const holdingPeriod = (exitDate.getTime() - entryDate.getTime()) / (1000 * 60 * 60 * 24);

    return {
      entryDate,
      exitDate,
      entryPrice,
      exitPrice: adjustedExitPrice,
      quantity,
      side,
      pnl,
      pnlPercent,
      exitReason,
      holdingPeriod
    };
  }

  /**
   * Calculate comprehensive performance metrics
   */
  private calculatePerformanceMetrics(
    trades: BacktestTrade[],
    equityCurve: { date: Date; equity: number; drawdown: number }[],
    initialCapital: number,
    riskFreeRate: number
  ): BacktestResults {
    const winningTrades = trades.filter(t => t.pnl > 0);
    const losingTrades = trades.filter(t => t.pnl < 0);
    
    const totalReturn = trades.reduce((sum, trade) => sum + trade.pnl, 0);
    const totalReturnPercent = (totalReturn / initialCapital) * 100;
    
    const maxDrawdown = Math.max(...equityCurve.map(e => e.drawdown));
    const maxDrawdownPercent = maxDrawdown;
    
    // Calculate Sharpe ratio
    const returns = equityCurve.map((point, index) => {
      if (index === 0) return 0;
      return (point.equity - equityCurve[index - 1].equity) / equityCurve[index - 1].equity;
    }).slice(1);
    
    const avgReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const returnStdDev = Math.sqrt(
      returns.reduce((sum, r) => sum + Math.pow(r - avgReturn, 2), 0) / returns.length
    );
    
    const sharpeRatio = returnStdDev > 0 ? (avgReturn - riskFreeRate / 252) / returnStdDev : 0;
    
    // Profit factor
    const grossProfit = winningTrades.reduce((sum, t) => sum + t.pnl, 0);
    const grossLoss = Math.abs(losingTrades.reduce((sum, t) => sum + t.pnl, 0));
    const profitFactor = grossLoss > 0 ? grossProfit / grossLoss : 0;
    
    // Monthly returns
    const monthlyReturns = this.calculateMonthlyReturns(equityCurve);

    return {
      totalTrades: trades.length,
      winningTrades: winningTrades.length,
      losingTrades: losingTrades.length,
      winRate: trades.length > 0 ? (winningTrades.length / trades.length) * 100 : 0,
      totalReturn,
      totalReturnPercent,
      maxDrawdown: maxDrawdown * initialCapital / 100,
      maxDrawdownPercent,
      sharpeRatio,
      profitFactor,
      averageWin: winningTrades.length > 0 ? grossProfit / winningTrades.length : 0,
      averageLoss: losingTrades.length > 0 ? grossLoss / losingTrades.length : 0,
      averageHoldingPeriod: trades.length > 0 ? 
        trades.reduce((sum, t) => sum + t.holdingPeriod, 0) / trades.length : 0,
      trades,
      equityCurve,
      monthlyReturns
    };
  }

  /**
   * Calculate monthly returns
   */
  private calculateMonthlyReturns(
    equityCurve: { date: Date; equity: number; drawdown: number }[]
  ): { month: string; return: number }[] {
    const monthlyData = new Map<string, { start: number; end: number }>();
    
    equityCurve.forEach(point => {
      const monthKey = `${point.date.getFullYear()}-${String(point.date.getMonth() + 1).padStart(2, '0')}`;
      
      if (!monthlyData.has(monthKey)) {
        monthlyData.set(monthKey, { start: point.equity, end: point.equity });
      } else {
        monthlyData.get(monthKey)!.end = point.equity;
      }
    });

    return Array.from(monthlyData.entries()).map(([month, data]) => ({
      month,
      return: ((data.end - data.start) / data.start) * 100
    }));
  }
}
