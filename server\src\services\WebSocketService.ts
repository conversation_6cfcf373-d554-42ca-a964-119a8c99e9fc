import { Server as SocketIOServer } from 'socket.io';
import { Server as HttpServer } from 'http';
import { logAPIRequest, logError } from '../utils/logger';

class WebSocketService {
  private io: SocketIOServer;

  constructor(server: HttpServer) {
    this.io = new SocketIOServer(server, {
      cors: {
        origin: ['http://localhost:3000', 'http://localhost:5173'], // Support both Vite dev server ports
        methods: ['GET', 'POST'],
        credentials: true
      },
    });

    this.initialize();
  }

  private initialize(): void {
    this.io.on('connection', (socket) => {
      console.log('Client connected:', socket.id);
      
      socket.on('disconnect', () => {
        console.log('Client disconnected:', socket.id);
      });
    });
  }

  public emit(event: string, data: any): void {
    this.io.emit(event, data);
  }
}

let webSocketService: WebSocketService;

export const initializeWebSocket = (server: HttpServer): WebSocketService => {
  if (!webSocketService) {
    webSocketService = new WebSocketService(server);
  }
  return webSocketService;
};

export const getWebSocketService = (): WebSocketService => {
  if (!webSocketService) {
    throw new Error('WebSocketService not initialized');
  }
  return webSocketService;
}; 