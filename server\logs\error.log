{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:30:19.621Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:30:25.537Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:30:31.581Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:30:37.741Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:30:43.855Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:30:49.850Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:30:55.731Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:31:12.621Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:31:44.343Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:32:10.917Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:32:17.167Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:32:23.050Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:32:29.301Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:32:35.312Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:33:17.143Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T12:33:37.398Z"}
{"level":"error","message":"Port 3001 is already in use.","service":"trading-bot","timestamp":"2025-06-22T12:37:37.744Z"}
{"level":"error","message":"Port 3001 is already in use.","service":"trading-bot","timestamp":"2025-06-22T12:40:37.174Z"}
{"level":"error","message":"Port 3001 is already in use.","service":"trading-bot","timestamp":"2025-06-22T12:41:01.971Z"}
{"level":"error","message":"Port 3001 is already in use.","service":"trading-bot","timestamp":"2025-06-22T12:49:51.793Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:00:10.019Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:00:15.950Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:00:22.019Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:00:28.078Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:00:46.771Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:00:53.994Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:01:11.339Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:01:17.305Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:01:23.426Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:01:29.290Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:01:35.227Z"}
{"level":"error","message":"Error parsing sentiment response: Unexpected non-whitespace character after JSON at position 3263 (line 32 column 1)","service":"trading-bot","stack":"SyntaxError: Unexpected non-whitespace character after JSON at position 3263 (line 32 column 1)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:01:51.306Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:02:57.846Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:03:18.182Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:03:39.145Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:03:56.729Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:04:06.397Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:04:12.693Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:04:18.950Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:04:44.641Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:04:50.406Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:04:56.346Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:05:23.160Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:06:21.998Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:06:29.245Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:07:13.494Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:07:19.232Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:07:25.262Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:07:31.187Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:07:37.219Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:07:43.062Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:07:49.128Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:07:55.707Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)","timestamp":"2025-06-22T13:08:01.729Z"}
[2025-06-22 16:15:10.209] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:15:16.468] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:15:22.696] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:15:28.902] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:15:35.079] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:15:51.322] ERROR: Error fetching historical data for INVALID: No data found, symbol may be delisted | {"stack":"Error: No data found, symbol may be delisted\n    at Object.<anonymous> (C:\\work\\Trading Bot\\server\\node_modules\\yahoo-finance2\\dist\\cjs\\src\\lib\\yahooFinanceFetch.js:108:27)\n    at Generator.next (<anonymous>)\n    at fulfilled (C:\\work\\Trading Bot\\server\\node_modules\\yahoo-finance2\\dist\\cjs\\src\\lib\\yahooFinanceFetch.js:5:58)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"}
[2025-06-22 16:15:52.437] ERROR: Error fetching historical data for INVALID: No data found, symbol may be delisted | {"stack":"Error: No data found, symbol may be delisted\n    at Object.<anonymous> (C:\\work\\Trading Bot\\server\\node_modules\\yahoo-finance2\\dist\\cjs\\src\\lib\\yahooFinanceFetch.js:108:27)\n    at Generator.next (<anonymous>)\n    at fulfilled (C:\\work\\Trading Bot\\server\\node_modules\\yahoo-finance2\\dist\\cjs\\src\\lib\\yahooFinanceFetch.js:5:58)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"}
[2025-06-22 16:15:52.770] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:15:58.738] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:16:06.312] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:16:38.195] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:16:44.236] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:16:50.189] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:16:56.426] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:17:02.631] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:18:30.313] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:18:48.570] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:19:20.382] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:19:39.678] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:19:45.475] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:20:00.207] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:20:05.935] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:20:31.768] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:20:37.743] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:20:43.854] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:21:31.040] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:22:11.949] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:22:18.963] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:22:45.855] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:22:51.284] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:22:57.238] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:23:02.965] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:23:09.160] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:23:21.934] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:23:28.101] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:23:34.860] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:23:48.342] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:24:12.470] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:25:01.942] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:30:20.530] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:30:26.455] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:30:32.597] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:30:38.525] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:30:53.077] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:31:00.852] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:31:18.417] ERROR: Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2) | {"stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:65:27)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:33:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:135:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:72:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:47:9)"}
[2025-06-22 16:37:52.832] ERROR: Port 3001 is already in use.
[2025-06-22 16:39:34.393] ERROR: Port 3001 is already in use.
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T13:45:19.540Z"}
{"level":"error","message":"Error parsing sentiment response: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"trading-bot","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at SentimentAnalysisService.parseSentimentResponse (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:48:35)\n    at SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:24:40)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\dist\\services\\SentimentAnalysisService.js:95:31)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:53:37)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\dist\\jobs\\dataUpdateJob.js:36:17)","timestamp":"2025-06-22T13:45:25.568Z"}
[2025-06-22 17:12:38.103] ERROR: Error parsing analysis response: Expected ',' or '}' after property value in JSON at position 3869 (line 69 column 26) | {"stack":"SyntaxError: Expected ',' or '}' after property value in JSON at position 3869 (line 69 column 26)\n    at JSON.parse (<anonymous>)\n    at NewsImpactAnalysisService.parseAnalysisResponse (C:\\work\\Trading Bot\\server\\src\\services\\NewsImpactAnalysisService.ts:204:27)\n    at NewsImpactAnalysisService.analyzeMarketImpact (C:\\work\\Trading Bot\\server\\src\\services\\NewsImpactAnalysisService.ts:67:29)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\work\\Trading Bot\\server\\src\\services\\NewsImpactAnalysisService.ts:258:26\n    at async Promise.all (index 1)\n    at async NewsImpactAnalysisService.batchAnalyzeArticles (C:\\work\\Trading Bot\\server\\src\\services\\NewsImpactAnalysisService.ts:262:28)\n    at async TickerDiscoveryService.discoverNewTickers (C:\\work\\Trading Bot\\server\\src\\services\\TickerDiscoveryService.ts:87:32)\n    at async C:\\work\\Trading Bot\\server\\src\\routes\\discovery.ts:27:31"}
[2025-06-22 17:12:41.284] ERROR: Error parsing analysis response: Expected ',' or '}' after property value in JSON at position 4345 (line 84 column 26) | {"stack":"SyntaxError: Expected ',' or '}' after property value in JSON at position 4345 (line 84 column 26)\n    at JSON.parse (<anonymous>)\n    at NewsImpactAnalysisService.parseAnalysisResponse (C:\\work\\Trading Bot\\server\\src\\services\\NewsImpactAnalysisService.ts:204:27)\n    at NewsImpactAnalysisService.analyzeMarketImpact (C:\\work\\Trading Bot\\server\\src\\services\\NewsImpactAnalysisService.ts:67:29)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\work\\Trading Bot\\server\\src\\services\\NewsImpactAnalysisService.ts:258:26\n    at async Promise.all (index 2)\n    at async NewsImpactAnalysisService.batchAnalyzeArticles (C:\\work\\Trading Bot\\server\\src\\services\\NewsImpactAnalysisService.ts:262:28)\n    at async TickerDiscoveryService.discoverNewTickers (C:\\work\\Trading Bot\\server\\src\\services\\TickerDiscoveryService.ts:87:32)\n    at async C:\\work\\Trading Bot\\server\\src\\routes\\discovery.ts:27:31"}
[2025-06-22 17:12:43.308] ERROR: Error parsing analysis response: Expected ',' or '}' after property value in JSON at position 4809 (line 91 column 27) | {"stack":"SyntaxError: Expected ',' or '}' after property value in JSON at position 4809 (line 91 column 27)\n    at JSON.parse (<anonymous>)\n    at NewsImpactAnalysisService.parseAnalysisResponse (C:\\work\\Trading Bot\\server\\src\\services\\NewsImpactAnalysisService.ts:204:27)\n    at NewsImpactAnalysisService.analyzeMarketImpact (C:\\work\\Trading Bot\\server\\src\\services\\NewsImpactAnalysisService.ts:67:29)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\work\\Trading Bot\\server\\src\\services\\NewsImpactAnalysisService.ts:258:26\n    at async Promise.all (index 0)\n    at async NewsImpactAnalysisService.batchAnalyzeArticles (C:\\work\\Trading Bot\\server\\src\\services\\NewsImpactAnalysisService.ts:262:28)\n    at async TickerDiscoveryService.discoverNewTickers (C:\\work\\Trading Bot\\server\\src\\services\\TickerDiscoveryService.ts:87:32)\n    at async C:\\work\\Trading Bot\\server\\src\\routes\\discovery.ts:27:31"}
[2025-06-22 17:13:05.783] ERROR: Error parsing analysis response: Expected ',' or '}' after property value in JSON at position 3910 (line 61 column 26) | {"stack":"SyntaxError: Expected ',' or '}' after property value in JSON at position 3910 (line 61 column 26)\n    at JSON.parse (<anonymous>)\n    at NewsImpactAnalysisService.parseAnalysisResponse (C:\\work\\Trading Bot\\server\\src\\services\\NewsImpactAnalysisService.ts:204:27)\n    at NewsImpactAnalysisService.analyzeMarketImpact (C:\\work\\Trading Bot\\server\\src\\services\\NewsImpactAnalysisService.ts:67:29)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\work\\Trading Bot\\server\\src\\services\\NewsImpactAnalysisService.ts:258:26\n    at async Promise.all (index 0)\n    at async NewsImpactAnalysisService.batchAnalyzeArticles (C:\\work\\Trading Bot\\server\\src\\services\\NewsImpactAnalysisService.ts:262:28)\n    at async TickerDiscoveryService.discoverNewTickers (C:\\work\\Trading Bot\\server\\src\\services\\TickerDiscoveryService.ts:87:32)\n    at async C:\\work\\Trading Bot\\server\\src\\routes\\discovery.ts:27:31"}
[2025-06-22 17:13:09.478] ERROR: Error parsing analysis response: Expected ',' or '}' after property value in JSON at position 4143 (line 69 column 27) | {"stack":"SyntaxError: Expected ',' or '}' after property value in JSON at position 4143 (line 69 column 27)\n    at JSON.parse (<anonymous>)\n    at NewsImpactAnalysisService.parseAnalysisResponse (C:\\work\\Trading Bot\\server\\src\\services\\NewsImpactAnalysisService.ts:204:27)\n    at NewsImpactAnalysisService.analyzeMarketImpact (C:\\work\\Trading Bot\\server\\src\\services\\NewsImpactAnalysisService.ts:67:29)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\work\\Trading Bot\\server\\src\\services\\NewsImpactAnalysisService.ts:258:26\n    at async Promise.all (index 2)\n    at async NewsImpactAnalysisService.batchAnalyzeArticles (C:\\work\\Trading Bot\\server\\src\\services\\NewsImpactAnalysisService.ts:262:28)\n    at async TickerDiscoveryService.discoverNewTickers (C:\\work\\Trading Bot\\server\\src\\services\\TickerDiscoveryService.ts:87:32)\n    at async C:\\work\\Trading Bot\\server\\src\\routes\\discovery.ts:27:31"}
[2025-06-22 17:13:10.767] ERROR: Error parsing analysis response: Expected ',' or '}' after property value in JSON at position 4211 (line 76 column 26) | {"stack":"SyntaxError: Expected ',' or '}' after property value in JSON at position 4211 (line 76 column 26)\n    at JSON.parse (<anonymous>)\n    at NewsImpactAnalysisService.parseAnalysisResponse (C:\\work\\Trading Bot\\server\\src\\services\\NewsImpactAnalysisService.ts:204:27)\n    at NewsImpactAnalysisService.analyzeMarketImpact (C:\\work\\Trading Bot\\server\\src\\services\\NewsImpactAnalysisService.ts:67:29)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\work\\Trading Bot\\server\\src\\services\\NewsImpactAnalysisService.ts:258:26\n    at async Promise.all (index 1)\n    at async NewsImpactAnalysisService.batchAnalyzeArticles (C:\\work\\Trading Bot\\server\\src\\services\\NewsImpactAnalysisService.ts:262:28)\n    at async TickerDiscoveryService.discoverNewTickers (C:\\work\\Trading Bot\\server\\src\\services\\TickerDiscoveryService.ts:87:32)\n    at async C:\\work\\Trading Bot\\server\\src\\routes\\discovery.ts:27:31"}
[2025-06-22 17:13:36.564] ERROR: Error parsing analysis response: Expected ',' or '}' after property value in JSON at position 4066 (line 76 column 26) | {"stack":"SyntaxError: Expected ',' or '}' after property value in JSON at position 4066 (line 76 column 26)\n    at JSON.parse (<anonymous>)\n    at NewsImpactAnalysisService.parseAnalysisResponse (C:\\work\\Trading Bot\\server\\src\\services\\NewsImpactAnalysisService.ts:204:27)\n    at NewsImpactAnalysisService.analyzeMarketImpact (C:\\work\\Trading Bot\\server\\src\\services\\NewsImpactAnalysisService.ts:67:29)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\work\\Trading Bot\\server\\src\\services\\NewsImpactAnalysisService.ts:258:26\n    at async Promise.all (index 0)\n    at async NewsImpactAnalysisService.batchAnalyzeArticles (C:\\work\\Trading Bot\\server\\src\\services\\NewsImpactAnalysisService.ts:262:28)\n    at async TickerDiscoveryService.discoverNewTickers (C:\\work\\Trading Bot\\server\\src\\services\\TickerDiscoveryService.ts:87:32)\n    at async C:\\work\\Trading Bot\\server\\src\\routes\\discovery.ts:27:31"}
[2025-06-22 17:13:39.111] ERROR: Error parsing analysis response: Expected ',' or '}' after property value in JSON at position 4084 (line 76 column 27) | {"stack":"SyntaxError: Expected ',' or '}' after property value in JSON at position 4084 (line 76 column 27)\n    at JSON.parse (<anonymous>)\n    at NewsImpactAnalysisService.parseAnalysisResponse (C:\\work\\Trading Bot\\server\\src\\services\\NewsImpactAnalysisService.ts:204:27)\n    at NewsImpactAnalysisService.analyzeMarketImpact (C:\\work\\Trading Bot\\server\\src\\services\\NewsImpactAnalysisService.ts:67:29)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\work\\Trading Bot\\server\\src\\services\\NewsImpactAnalysisService.ts:258:26\n    at async Promise.all (index 1)\n    at async NewsImpactAnalysisService.batchAnalyzeArticles (C:\\work\\Trading Bot\\server\\src\\services\\NewsImpactAnalysisService.ts:262:28)\n    at async TickerDiscoveryService.discoverNewTickers (C:\\work\\Trading Bot\\server\\src\\services\\TickerDiscoveryService.ts:87:32)\n    at async C:\\work\\Trading Bot\\server\\src\\routes\\discovery.ts:27:31"}
[2025-06-22 18:22:08.562] [ERROR] [General]: Error analyzing market impact: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemma-3-27b-it:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count","quotaId":"GenerateContentInputTokensPerModelPerMinute-FreeTier","quotaDimensions":{"model":"gemma-3-27b","location":"global"},"quotaValue":"15000"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"51s"}] | {"service":"trading-bot","status":429,"statusText":"Too Many Requests","errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count","quotaId":"GenerateContentInputTokensPerModelPerMinute-FreeTier","quotaDimensions":{"model":"gemma-3-27b","location":"global"},"quotaValue":"15000"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"51s"}],"stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemma-3-27b-it:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count\",\"quotaId\":\"GenerateContentInputTokensPerModelPerMinute-FreeTier\",\"quotaDimensions\":{\"model\":\"gemma-3-27b\",\"location\":\"global\"},\"quotaValue\":\"15000\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"51s\"}]\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async NewsImpactAnalysisService.analyzeMarketImpact (C:\\work\\Trading Bot\\server\\src\\services\\NewsImpactAnalysisService.ts:63:22)\n    at async C:\\work\\Trading Bot\\server\\src\\services\\NewsImpactAnalysisService.ts:298:26\n    at async Promise.all (index 1)\n    at async NewsImpactAnalysisService.batchAnalyzeArticles (C:\\work\\Trading Bot\\server\\src\\services\\NewsImpactAnalysisService.ts:302:28)\n    at async TickerDiscoveryService.discoverNewTickers (C:\\work\\Trading Bot\\server\\src\\services\\TickerDiscoveryService.ts:97:32)\n    at async C:\\work\\Trading Bot\\server\\src\\routes\\discovery.ts:31:31"}
[2025-06-22 18:22:08.622] [ERROR] [General]: Error analyzing market impact: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemma-3-27b-it:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count","quotaId":"GenerateContentInputTokensPerModelPerMinute-FreeTier","quotaDimensions":{"location":"global","model":"gemma-3-27b"},"quotaValue":"15000"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"51s"}] | {"service":"trading-bot","status":429,"statusText":"Too Many Requests","errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count","quotaId":"GenerateContentInputTokensPerModelPerMinute-FreeTier","quotaDimensions":{"location":"global","model":"gemma-3-27b"},"quotaValue":"15000"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"51s"}],"stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemma-3-27b-it:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count\",\"quotaId\":\"GenerateContentInputTokensPerModelPerMinute-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemma-3-27b\"},\"quotaValue\":\"15000\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"51s\"}]\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async NewsImpactAnalysisService.analyzeMarketImpact (C:\\work\\Trading Bot\\server\\src\\services\\NewsImpactAnalysisService.ts:63:22)\n    at async C:\\work\\Trading Bot\\server\\src\\services\\NewsImpactAnalysisService.ts:298:26\n    at async Promise.all (index 0)\n    at async NewsImpactAnalysisService.batchAnalyzeArticles (C:\\work\\Trading Bot\\server\\src\\services\\NewsImpactAnalysisService.ts:302:28)\n    at async TickerDiscoveryService.discoverNewTickers (C:\\work\\Trading Bot\\server\\src\\services\\TickerDiscoveryService.ts:97:32)\n    at async C:\\work\\Trading Bot\\server\\src\\routes\\discovery.ts:31:31"}
[2025-06-22 18:22:08.889] [ERROR] [General]: Error analyzing market impact: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemma-3-27b-it:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count","quotaId":"GenerateContentInputTokensPerModelPerMinute-FreeTier","quotaDimensions":{"model":"gemma-3-27b","location":"global"},"quotaValue":"15000"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"51s"}] | {"service":"trading-bot","status":429,"statusText":"Too Many Requests","errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count","quotaId":"GenerateContentInputTokensPerModelPerMinute-FreeTier","quotaDimensions":{"model":"gemma-3-27b","location":"global"},"quotaValue":"15000"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"51s"}],"stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemma-3-27b-it:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count\",\"quotaId\":\"GenerateContentInputTokensPerModelPerMinute-FreeTier\",\"quotaDimensions\":{\"model\":\"gemma-3-27b\",\"location\":\"global\"},\"quotaValue\":\"15000\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"51s\"}]\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async NewsImpactAnalysisService.analyzeMarketImpact (C:\\work\\Trading Bot\\server\\src\\services\\NewsImpactAnalysisService.ts:63:22)\n    at async C:\\work\\Trading Bot\\server\\src\\services\\NewsImpactAnalysisService.ts:298:26\n    at async Promise.all (index 2)\n    at async NewsImpactAnalysisService.batchAnalyzeArticles (C:\\work\\Trading Bot\\server\\src\\services\\NewsImpactAnalysisService.ts:302:28)\n    at async TickerDiscoveryService.discoverNewTickers (C:\\work\\Trading Bot\\server\\src\\services\\TickerDiscoveryService.ts:97:32)\n    at async C:\\work\\Trading Bot\\server\\src\\routes\\discovery.ts:31:31"}
[2025-06-22 18:22:10.008] [ERROR] [General]: Error analyzing market impact: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemma-3-27b-it:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count","quotaId":"GenerateContentInputTokensPerModelPerMinute-FreeTier","quotaDimensions":{"location":"global","model":"gemma-3-27b"},"quotaValue":"15000"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"50s"}] | {"service":"trading-bot","status":429,"statusText":"Too Many Requests","errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count","quotaId":"GenerateContentInputTokensPerModelPerMinute-FreeTier","quotaDimensions":{"location":"global","model":"gemma-3-27b"},"quotaValue":"15000"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"50s"}],"stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemma-3-27b-it:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count\",\"quotaId\":\"GenerateContentInputTokensPerModelPerMinute-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemma-3-27b\"},\"quotaValue\":\"15000\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"50s\"}]\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async NewsImpactAnalysisService.analyzeMarketImpact (C:\\work\\Trading Bot\\server\\src\\services\\NewsImpactAnalysisService.ts:63:22)\n    at async C:\\work\\Trading Bot\\server\\src\\services\\NewsImpactAnalysisService.ts:298:26\n    at async Promise.all (index 1)\n    at async NewsImpactAnalysisService.batchAnalyzeArticles (C:\\work\\Trading Bot\\server\\src\\services\\NewsImpactAnalysisService.ts:302:28)\n    at async TickerDiscoveryService.discoverNewTickers (C:\\work\\Trading Bot\\server\\src\\services\\TickerDiscoveryService.ts:97:32)\n    at async C:\\work\\Trading Bot\\server\\src\\routes\\discovery.ts:31:31"}
[2025-06-22 18:22:10.024] [ERROR] [General]: Error analyzing market impact: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemma-3-27b-it:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count","quotaId":"GenerateContentInputTokensPerModelPerMinute-FreeTier","quotaDimensions":{"location":"global","model":"gemma-3-27b"},"quotaValue":"15000"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"50s"}] | {"service":"trading-bot","status":429,"statusText":"Too Many Requests","errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count","quotaId":"GenerateContentInputTokensPerModelPerMinute-FreeTier","quotaDimensions":{"location":"global","model":"gemma-3-27b"},"quotaValue":"15000"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"50s"}],"stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemma-3-27b-it:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count\",\"quotaId\":\"GenerateContentInputTokensPerModelPerMinute-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemma-3-27b\"},\"quotaValue\":\"15000\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"50s\"}]\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async NewsImpactAnalysisService.analyzeMarketImpact (C:\\work\\Trading Bot\\server\\src\\services\\NewsImpactAnalysisService.ts:63:22)\n    at async C:\\work\\Trading Bot\\server\\src\\services\\NewsImpactAnalysisService.ts:298:26\n    at async Promise.all (index 0)\n    at async NewsImpactAnalysisService.batchAnalyzeArticles (C:\\work\\Trading Bot\\server\\src\\services\\NewsImpactAnalysisService.ts:302:28)\n    at async TickerDiscoveryService.discoverNewTickers (C:\\work\\Trading Bot\\server\\src\\services\\TickerDiscoveryService.ts:97:32)\n    at async C:\\work\\Trading Bot\\server\\src\\routes\\discovery.ts:31:31"}
[2025-06-22 18:22:16.085] [ERROR] [General]: Error analyzing market impact: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemma-3-27b-it:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count","quotaId":"GenerateContentInputTokensPerModelPerMinute-FreeTier","quotaDimensions":{"location":"global","model":"gemma-3-27b"},"quotaValue":"15000"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"44s"}] | {"service":"trading-bot","status":429,"statusText":"Too Many Requests","errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count","quotaId":"GenerateContentInputTokensPerModelPerMinute-FreeTier","quotaDimensions":{"location":"global","model":"gemma-3-27b"},"quotaValue":"15000"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"44s"}],"stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemma-3-27b-it:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count\",\"quotaId\":\"GenerateContentInputTokensPerModelPerMinute-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemma-3-27b\"},\"quotaValue\":\"15000\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"44s\"}]\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async NewsImpactAnalysisService.analyzeMarketImpact (C:\\work\\Trading Bot\\server\\src\\services\\NewsImpactAnalysisService.ts:63:22)\n    at async C:\\work\\Trading Bot\\server\\src\\services\\NewsImpactAnalysisService.ts:298:26\n    at async Promise.all (index 1)\n    at async NewsImpactAnalysisService.batchAnalyzeArticles (C:\\work\\Trading Bot\\server\\src\\services\\NewsImpactAnalysisService.ts:302:28)\n    at async TickerDiscoveryService.discoverNewTickers (C:\\work\\Trading Bot\\server\\src\\services\\TickerDiscoveryService.ts:97:32)\n    at async C:\\work\\Trading Bot\\server\\src\\routes\\discovery.ts:31:31"}
[2025-06-22 18:22:16.403] [ERROR] [General]: Error analyzing market impact: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemma-3-27b-it:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count","quotaId":"GenerateContentInputTokensPerModelPerMinute-FreeTier","quotaDimensions":{"model":"gemma-3-27b","location":"global"},"quotaValue":"15000"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"44s"}] | {"service":"trading-bot","status":429,"statusText":"Too Many Requests","errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count","quotaId":"GenerateContentInputTokensPerModelPerMinute-FreeTier","quotaDimensions":{"model":"gemma-3-27b","location":"global"},"quotaValue":"15000"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"44s"}],"stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemma-3-27b-it:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count\",\"quotaId\":\"GenerateContentInputTokensPerModelPerMinute-FreeTier\",\"quotaDimensions\":{\"model\":\"gemma-3-27b\",\"location\":\"global\"},\"quotaValue\":\"15000\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"44s\"}]\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async NewsImpactAnalysisService.analyzeMarketImpact (C:\\work\\Trading Bot\\server\\src\\services\\NewsImpactAnalysisService.ts:63:22)\n    at async C:\\work\\Trading Bot\\server\\src\\services\\NewsImpactAnalysisService.ts:298:26\n    at async Promise.all (index 0)\n    at async NewsImpactAnalysisService.batchAnalyzeArticles (C:\\work\\Trading Bot\\server\\src\\services\\NewsImpactAnalysisService.ts:302:28)\n    at async TickerDiscoveryService.discoverNewTickers (C:\\work\\Trading Bot\\server\\src\\services\\TickerDiscoveryService.ts:97:32)\n    at async C:\\work\\Trading Bot\\server\\src\\routes\\discovery.ts:31:31"}
[2025-06-22 18:27:09.217] [ERROR] [General]: Port 3001 is already in use. | {"service":"trading-bot"}
[2025-06-22 18:39:35.513] [ERROR] [General]: Port 3001 is already in use. | {"service":"trading-bot"}
[2025-06-22 18:43:27.987] [ERROR] [General]: Port 3001 is already in use. | {"service":"trading-bot"}
[2025-06-22 21:31:21.944] [ERROR] [General]: Port 3001 is already in use. | {"service":"trading-bot"}
[2025-06-22 21:31:29.683] [ERROR] [General]: Port 3001 is already in use. | {"service":"trading-bot"}
[2025-06-22 21:32:33.813] [ERROR] [General]: Port 3001 is already in use. | {"service":"trading-bot"}
[2025-06-22 22:30:02.549] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:02.813] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:02.915] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:03.195] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:03.326] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:03.665] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:03.761] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:04.059] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:05.640] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:06.028] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:06.134] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:06.394] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:06.540] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:06.794] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:06.952] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:07.207] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:07.342] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:07.574] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:07.725] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:07.959] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:08.084] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:08.348] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:08.470] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:10.499] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:10.759] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:10.913] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:12.336] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:12.629] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:12.742] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:13.059] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:13.204] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:13.489] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:13.582] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:13.910] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:14.018] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:14.409] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:14.509] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:14.829] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:14.931] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:15.188] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:15.337] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:15.564] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:16.870] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:17.152] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:17.253] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:17.594] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:17.691] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:17.957] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:18.048] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:18.329] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:18.420] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:18.720] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:18.821] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:19.102] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:19.197] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:19.540] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:19.633] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:19.895] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:20.022] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:20.265] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:21.455] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:22.635] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:23.840] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:24.121] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:24.216] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:24.557] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:25.597] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:25.992] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:26.094] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:26.399] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:26.507] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:26.745] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
[2025-06-22 22:30:26.910] [ERROR] [General]: Error in sentiment analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods. | {"service":"trading-bot","status":404,"statusText":"Not Found","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-preview-0514:generateContent: [404 Not Found] models/gemini-1.5-pro-preview-0514 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at _makeRequestInternal (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:353:19)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async generateContent (C:\\work\\Trading Bot\\server\\node_modules\\@google\\generative-ai\\dist\\index.js:752:22)\n    at async SentimentAnalysisService.analyzeSentiment (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:28:22)\n    at async SentimentAnalysisService.analyzeMultipleArticles (C:\\work\\Trading Bot\\server\\src\\services\\SentimentAnalysisService.ts:147:25)\n    at async DataUpdateJob.updateTicker (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:81:31)\n    at async DataUpdateJob.updateAllTickerData (C:\\work\\Trading Bot\\server\\src\\jobs\\dataUpdateJob.ts:56:9)"}
