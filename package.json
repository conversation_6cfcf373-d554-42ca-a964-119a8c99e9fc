{"name": "trading-bot", "version": "1.0.0", "description": "Trading Bot with Technical Analysis & News Sentiment Recommendations", "main": "dist/server/index.js", "scripts": {"dev": "concurrently \"npm run dev:server\" \"npm run dev:client\"", "dev:server": "cd server && npm run dev", "dev:client": "cd client && npm run dev", "build": "npm run build:server && npm run build:client", "build:server": "cd server && npm run build", "build:client": "cd client && npm run build", "start": "cd server && npm start", "install:all": "npm install && cd server && npm install && cd ../client && npm install"}, "keywords": ["trading", "bot", "technical-analysis", "sentiment", "react", "typescript"], "author": "Trading Bot Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "dependencies": {"socket.io": "^4.8.1"}}