import React from 'react';
import { Loader2, BarChart3, TrendingUp } from 'lucide-react';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  text?: string;
  variant?: 'default' | 'chart' | 'data';
  className?: string;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  text,
  variant = 'default',
  className = ''
}) => {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-8 w-8',
    lg: 'h-12 w-12'
  };

  const getIcon = () => {
    switch (variant) {
      case 'chart':
        return <BarChart3 className={`${sizeClasses[size]} animate-pulse text-primary-600`} />;
      case 'data':
        return <TrendingUp className={`${sizeClasses[size]} animate-pulse text-primary-600`} />;
      default:
        return <Loader2 className={`${sizeClasses[size]} animate-spin text-primary-600`} />;
    }
  };

  const getText = () => {
    if (text) return text;
    
    switch (variant) {
      case 'chart':
        return 'Loading chart...';
      case 'data':
        return 'Loading data...';
      default:
        return 'Loading...';
    }
  };

  return (
    <div className={`flex flex-col items-center justify-center space-y-3 ${className}`}>
      {getIcon()}
      {text !== null && (
        <p className="text-sm text-gray-600 animate-pulse">{getText()}</p>
      )}
    </div>
  );
};

// Specialized loading components
export const ChartLoader: React.FC<{ height?: number; text?: string }> = ({ 
  height = 400, 
  text = "Loading chart..." 
}) => (
  <div 
    className="flex items-center justify-center bg-gray-50 border border-gray-200 rounded-lg"
    style={{ height }}
  >
    <LoadingSpinner variant="chart" size="lg" text={text} />
  </div>
);

export const DataLoader: React.FC<{ text?: string; className?: string }> = ({ 
  text = "Loading data...", 
  className = "py-8" 
}) => (
  <div className={`flex items-center justify-center ${className}`}>
    <LoadingSpinner variant="data" size="md" text={text} />
  </div>
);

export const PageLoader: React.FC<{ text?: string }> = ({ 
  text = "Loading..." 
}) => (
  <div className="flex items-center justify-center min-h-screen">
    <LoadingSpinner size="lg" text={text} />
  </div>
);

// Skeleton loaders for better UX
export const SkeletonCard: React.FC = () => (
  <div className="animate-pulse">
    <div className="bg-gray-200 rounded-lg p-6">
      <div className="h-4 bg-gray-300 rounded w-3/4 mb-4"></div>
      <div className="h-8 bg-gray-300 rounded w-1/2 mb-2"></div>
      <div className="h-3 bg-gray-300 rounded w-1/4"></div>
    </div>
  </div>
);

export const SkeletonChart: React.FC<{ height?: number }> = ({ height = 400 }) => (
  <div className="animate-pulse">
    <div className="bg-gray-200 rounded-lg mb-4" style={{ height }}>
      <div className="flex items-end justify-center h-full p-8 space-x-2">
        {Array.from({ length: 20 }).map((_, i) => (
          <div
            key={i}
            className="bg-gray-300 rounded-t"
            style={{
              height: `${Math.random() * 60 + 20}%`,
              width: '8px'
            }}
          />
        ))}
      </div>
    </div>
    <div className="grid grid-cols-4 gap-4">
      {Array.from({ length: 4 }).map((_, i) => (
        <div key={i} className="bg-gray-200 rounded p-3">
          <div className="h-3 bg-gray-300 rounded w-1/2 mb-2"></div>
          <div className="h-6 bg-gray-300 rounded w-3/4"></div>
        </div>
      ))}
    </div>
  </div>
);
