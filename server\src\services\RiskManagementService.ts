import { OHLCV, AdvancedTechnicalIndicators, SignalStrength } from '../types';
import { logger } from '../utils/logger';

export interface RiskMetrics {
  stopLoss: number;
  takeProfit: number;
  positionSize: number;
  maxRisk: number;
  riskRewardRatio: number;
  kellyPercentage: number;
  volatilityAdjustedSize: number;
  maxDrawdown: number;
  sharpeRatio: number;
  valueAtRisk: number; // VaR at 95% confidence
  expectedShortfall: number; // CVaR
}

export interface PortfolioRisk {
  totalExposure: number;
  diversificationRatio: number;
  correlationRisk: number;
  sectorConcentration: { [sector: string]: number };
  maxPositionSize: number;
  riskBudget: number;
  leverageRatio: number;
}

export interface RiskManagementConfig {
  maxRiskPerTrade: number; // 0.01 = 1%
  maxPortfolioRisk: number; // 0.05 = 5%
  maxDrawdown: number; // 0.20 = 20%
  riskFreeRate: number; // 0.02 = 2%
  confidenceLevel: number; // 0.95 = 95%
  lookbackPeriod: number; // days
  rebalanceFrequency: number; // days
  maxLeverage: number; // 2.0 = 2x
  maxSectorConcentration: number; // 0.30 = 30%
}

export class RiskManagementService {
  private config: RiskManagementConfig;

  constructor(config: RiskManagementConfig) {
    this.config = config;
  }

  /**
   * Calculate comprehensive risk metrics for a position
   */
  public calculateRiskMetrics(
    currentPrice: number,
    indicators: AdvancedTechnicalIndicators,
    signalStrength: SignalStrength,
    historicalData: OHLCV[],
    portfolioValue: number,
    winRate?: number,
    avgWin?: number,
    avgLoss?: number
  ): RiskMetrics {
    try {
      // Calculate ATR-based stop loss and take profit
      const atrMultiplier = this.getATRMultiplier(signalStrength.overall);
      const stopLoss = this.calculateStopLoss(currentPrice, indicators.atr, atrMultiplier, signalStrength.overall > 50);
      const takeProfit = this.calculateTakeProfit(currentPrice, indicators.atr, atrMultiplier, signalStrength.overall > 50);
      
      // Calculate risk-reward ratio
      const riskRewardRatio = this.calculateRiskRewardRatio(currentPrice, stopLoss, takeProfit, signalStrength.overall > 50);
      
      // Calculate Kelly percentage if win rate data is available
      const kellyPercentage = this.calculateKellyPercentage(winRate, avgWin, avgLoss, riskRewardRatio);
      
      // Calculate position size using multiple methods
      const basicPositionSize = this.calculateBasicPositionSize(portfolioValue, currentPrice, stopLoss, signalStrength.overall > 50);
      const volatilityAdjustedSize = this.calculateVolatilityAdjustedSize(basicPositionSize, indicators.atr, currentPrice);
      const positionSize = Math.min(basicPositionSize, volatilityAdjustedSize);
      
      // Calculate portfolio-level risk metrics
      const maxDrawdown = this.calculateMaxDrawdown(historicalData);
      const sharpeRatio = this.calculateSharpeRatio(historicalData);
      const valueAtRisk = this.calculateVaR(historicalData, portfolioValue);
      const expectedShortfall = this.calculateExpectedShortfall(historicalData, portfolioValue);
      
      const maxRisk = this.config.maxRiskPerTrade;

      logger.info('Risk metrics calculated', {
        positionSize,
        stopLoss,
        takeProfit,
        riskRewardRatio,
        kellyPercentage
      });

      return {
        stopLoss,
        takeProfit,
        positionSize,
        maxRisk,
        riskRewardRatio,
        kellyPercentage,
        volatilityAdjustedSize,
        maxDrawdown,
        sharpeRatio,
        valueAtRisk,
        expectedShortfall
      };
    } catch (error) {
      logger.error('Error calculating risk metrics:', error);
      throw error;
    }
  }

  /**
   * Calculate Kelly Criterion percentage
   */
  private calculateKellyPercentage(
    winRate?: number,
    avgWin?: number,
    avgLoss?: number,
    riskRewardRatio?: number
  ): number {
    if (!winRate || !avgWin || !avgLoss) {
      // Use simplified Kelly with risk-reward ratio
      if (riskRewardRatio && riskRewardRatio > 1) {
        const estimatedWinRate = 0.55; // Conservative estimate
        return Math.max(0, Math.min(0.25, (estimatedWinRate * riskRewardRatio - (1 - estimatedWinRate)) / riskRewardRatio));
      }
      return 0.02; // Default 2%
    }

    // Full Kelly formula: f = (bp - q) / b
    // where b = odds (avgWin/avgLoss), p = win rate, q = loss rate
    const b = avgWin / avgLoss;
    const p = winRate;
    const q = 1 - winRate;
    
    const kelly = (b * p - q) / b;
    
    // Cap Kelly at 25% for safety (fractional Kelly)
    return Math.max(0, Math.min(0.25, kelly));
  }

  /**
   * Calculate ATR multiplier based on signal strength
   */
  private getATRMultiplier(signalStrength: number): number {
    // Higher signal strength = tighter stops
    if (signalStrength > 80) return 1.5;
    if (signalStrength > 60) return 2.0;
    if (signalStrength > 40) return 2.5;
    return 3.0;
  }

  /**
   * Calculate stop loss using ATR
   */
  private calculateStopLoss(
    currentPrice: number,
    atr: number,
    atrMultiplier: number,
    isLong: boolean
  ): number {
    const stopDistance = atr * atrMultiplier;
    return isLong ? currentPrice - stopDistance : currentPrice + stopDistance;
  }

  /**
   * Calculate take profit using ATR
   */
  private calculateTakeProfit(
    currentPrice: number,
    atr: number,
    atrMultiplier: number,
    isLong: boolean
  ): number {
    const profitDistance = atr * atrMultiplier * 2; // 2:1 risk-reward minimum
    return isLong ? currentPrice + profitDistance : currentPrice - profitDistance;
  }

  /**
   * Calculate risk-reward ratio
   */
  private calculateRiskRewardRatio(
    currentPrice: number,
    stopLoss: number,
    takeProfit: number,
    isLong: boolean
  ): number {
    const risk = Math.abs(currentPrice - stopLoss);
    const reward = Math.abs(takeProfit - currentPrice);
    return risk > 0 ? reward / risk : 0;
  }

  /**
   * Calculate basic position size based on risk per trade
   */
  private calculateBasicPositionSize(
    portfolioValue: number,
    currentPrice: number,
    stopLoss: number,
    isLong: boolean
  ): number {
    const riskAmount = portfolioValue * this.config.maxRiskPerTrade;
    const riskPerShare = Math.abs(currentPrice - stopLoss);
    
    if (riskPerShare === 0) return 0;
    
    const shares = Math.floor(riskAmount / riskPerShare);
    return shares * currentPrice;
  }

  /**
   * Calculate volatility-adjusted position size
   */
  private calculateVolatilityAdjustedSize(
    baseSize: number,
    atr: number,
    currentPrice: number
  ): number {
    // Reduce position size for high volatility stocks
    const volatilityRatio = atr / currentPrice;
    
    if (volatilityRatio > 0.05) { // Very high volatility
      return baseSize * 0.5;
    } else if (volatilityRatio > 0.03) { // High volatility
      return baseSize * 0.7;
    } else if (volatilityRatio > 0.02) { // Medium volatility
      return baseSize * 0.85;
    }
    
    return baseSize; // Low volatility
  }

  /**
   * Calculate maximum drawdown from historical data
   */
  private calculateMaxDrawdown(historicalData: OHLCV[]): number {
    if (historicalData.length < 2) return 0;

    const prices = historicalData.map(d => d.close);
    let maxDrawdown = 0;
    let peak = prices[0];

    for (let i = 1; i < prices.length; i++) {
      if (prices[i] > peak) {
        peak = prices[i];
      } else {
        const drawdown = (peak - prices[i]) / peak;
        maxDrawdown = Math.max(maxDrawdown, drawdown);
      }
    }

    return maxDrawdown;
  }

  /**
   * Calculate Sharpe ratio
   */
  private calculateSharpeRatio(historicalData: OHLCV[]): number {
    if (historicalData.length < 2) return 0;

    const returns = [];
    for (let i = 1; i < historicalData.length; i++) {
      const dailyReturn = (historicalData[i].close - historicalData[i - 1].close) / historicalData[i - 1].close;
      returns.push(dailyReturn);
    }

    const avgReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const variance = returns.reduce((sum, r) => sum + Math.pow(r - avgReturn, 2), 0) / returns.length;
    const stdDev = Math.sqrt(variance);

    if (stdDev === 0) return 0;

    // Annualized Sharpe ratio
    const annualizedReturn = avgReturn * 252;
    const annualizedStdDev = stdDev * Math.sqrt(252);
    
    return (annualizedReturn - this.config.riskFreeRate) / annualizedStdDev;
  }

  /**
   * Calculate Value at Risk (VaR) at specified confidence level
   */
  private calculateVaR(historicalData: OHLCV[], portfolioValue: number): number {
    if (historicalData.length < 2) return 0;

    const returns = [];
    for (let i = 1; i < historicalData.length; i++) {
      const dailyReturn = (historicalData[i].close - historicalData[i - 1].close) / historicalData[i - 1].close;
      returns.push(dailyReturn);
    }

    // Sort returns in ascending order
    returns.sort((a, b) => a - b);
    
    // Find the percentile corresponding to confidence level
    const percentileIndex = Math.floor((1 - this.config.confidenceLevel) * returns.length);
    const varReturn = returns[percentileIndex] || 0;
    
    return Math.abs(varReturn * portfolioValue);
  }

  /**
   * Calculate Expected Shortfall (Conditional VaR)
   */
  private calculateExpectedShortfall(historicalData: OHLCV[], portfolioValue: number): number {
    if (historicalData.length < 2) return 0;

    const returns = [];
    for (let i = 1; i < historicalData.length; i++) {
      const dailyReturn = (historicalData[i].close - historicalData[i - 1].close) / historicalData[i - 1].close;
      returns.push(dailyReturn);
    }

    // Sort returns in ascending order
    returns.sort((a, b) => a - b);
    
    // Calculate average of worst returns beyond VaR
    const percentileIndex = Math.floor((1 - this.config.confidenceLevel) * returns.length);
    const worstReturns = returns.slice(0, percentileIndex);
    
    if (worstReturns.length === 0) return 0;
    
    const avgWorstReturn = worstReturns.reduce((sum, r) => sum + r, 0) / worstReturns.length;
    
    return Math.abs(avgWorstReturn * portfolioValue);
  }

  /**
   * Assess portfolio-level risk
   */
  public assessPortfolioRisk(
    positions: { ticker: string; value: number; sector: string }[],
    totalValue: number
  ): PortfolioRisk {
    const totalExposure = positions.reduce((sum, pos) => sum + pos.value, 0);
    
    // Calculate sector concentration
    const sectorConcentration: { [sector: string]: number } = {};
    positions.forEach(pos => {
      if (!sectorConcentration[pos.sector]) {
        sectorConcentration[pos.sector] = 0;
      }
      sectorConcentration[pos.sector] += pos.value / totalValue;
    });

    // Calculate diversification ratio (simplified)
    const numPositions = positions.length;
    const avgPositionSize = totalExposure / numPositions;
    const diversificationRatio = numPositions > 0 ? 1 / Math.sqrt(numPositions) : 0;

    // Calculate correlation risk (simplified - would need actual correlation matrix)
    const correlationRisk = Math.max(...Object.values(sectorConcentration));

    const maxPositionSize = Math.max(...positions.map(pos => pos.value / totalValue));
    const leverageRatio = totalExposure / totalValue;
    const riskBudget = this.config.maxPortfolioRisk;

    return {
      totalExposure,
      diversificationRatio,
      correlationRisk,
      sectorConcentration,
      maxPositionSize,
      riskBudget,
      leverageRatio
    };
  }

  /**
   * Generate risk management recommendations
   */
  public generateRiskRecommendations(
    riskMetrics: RiskMetrics,
    portfolioRisk: PortfolioRisk
  ): string[] {
    const recommendations: string[] = [];

    // Position size recommendations
    if (riskMetrics.kellyPercentage > 0.15) {
      recommendations.push('Consider reducing position size - Kelly percentage suggests high risk');
    }

    // Risk-reward recommendations
    if (riskMetrics.riskRewardRatio < 1.5) {
      recommendations.push('Risk-reward ratio below 1.5:1 - consider tighter stops or wider targets');
    }

    // Portfolio concentration recommendations
    if (portfolioRisk.maxPositionSize > 0.20) {
      recommendations.push('Single position exceeds 20% of portfolio - consider reducing concentration');
    }

    // Sector concentration recommendations
    Object.entries(portfolioRisk.sectorConcentration).forEach(([sector, concentration]) => {
      if (concentration > this.config.maxSectorConcentration) {
        recommendations.push(`${sector} sector concentration exceeds ${(this.config.maxSectorConcentration * 100).toFixed(0)}%`);
      }
    });

    // Leverage recommendations
    if (portfolioRisk.leverageRatio > this.config.maxLeverage) {
      recommendations.push('Portfolio leverage exceeds maximum allowed - reduce positions');
    }

    // Volatility recommendations
    if (riskMetrics.sharpeRatio < 0.5) {
      recommendations.push('Low Sharpe ratio indicates poor risk-adjusted returns');
    }

    // VaR recommendations
    if (riskMetrics.valueAtRisk > portfolioRisk.totalExposure * 0.05) {
      recommendations.push('Value at Risk exceeds 5% of portfolio - consider reducing risk');
    }

    return recommendations;
  }
}
