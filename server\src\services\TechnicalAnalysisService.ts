import { RSI, MACD, BollingerBands, SMA, ATR } from 'technicalindicators';
import { OHLCV, TechnicalIndicators, SignalType } from '../types';
import { SETTINGS } from '../config/settings';
import { logger } from '../utils/logger';

export class TechnicalAnalysisService {
  /**
   * Calculate RSI (Relative Strength Index)
   */
  private calculateRSI(prices: number[]): number {
    try {
      const rsiValues = RSI.calculate({
        values: prices,
        period: SETTINGS.technicalIndicators.rsi.period
      });
      
      return rsiValues[rsiValues.length - 1] || 50;
    } catch (error) {
      logger.error('Error calculating RSI:', error);
      return 50;
    }
  }

  /**
   * Calculate MACD (Moving Average Convergence Divergence)
   */
  private calculateMACD(prices: number[]): { macd: number; signal: number; histogram: number } {
    try {
      const macdValues = MACD.calculate({
        values: prices,
        fastPeriod: SETTINGS.technicalIndicators.macd.fastPeriod,
        slowPeriod: SETTINGS.technicalIndicators.macd.slowPeriod,
        signalPeriod: SETTINGS.technicalIndicators.macd.signalPeriod,
        SimpleMAOscillator: false,
        SimpleMASignal: false
      });
      
      const lastMACD = macdValues[macdValues.length - 1];
      return {
        macd: lastMACD?.MACD || 0,
        signal: lastMACD?.signal || 0,
        histogram: lastMACD?.histogram || 0
      };
    } catch (error) {
      logger.error('Error calculating MACD:', error);
      return { macd: 0, signal: 0, histogram: 0 };
    }
  }

  /**
   * Calculate Bollinger Bands
   */
  private calculateBollingerBands(prices: number[]): { upper: number; middle: number; lower: number } {
    try {
      const bbValues = BollingerBands.calculate({
        values: prices,
        period: SETTINGS.technicalIndicators.bollingerBands.period,
        stdDev: SETTINGS.technicalIndicators.bollingerBands.stdDev
      });
      
      const lastBB = bbValues[bbValues.length - 1];
      return {
        upper: lastBB?.upper || 0,
        middle: lastBB?.middle || 0,
        lower: lastBB?.lower || 0
      };
    } catch (error) {
      logger.error('Error calculating Bollinger Bands:', error);
      return { upper: 0, middle: 0, lower: 0 };
    }
  }

  /**
   * Calculate Simple Moving Averages
   */
  private calculateSMA(prices: number[]): { short: number; long: number; crossover: SignalType } {
    try {
      const shortSMA = SMA.calculate({
        values: prices,
        period: SETTINGS.technicalIndicators.sma.shortPeriod
      });
      
      const longSMA = SMA.calculate({
        values: prices,
        period: SETTINGS.technicalIndicators.sma.longPeriod
      });
      
      const shortValue = shortSMA[shortSMA.length - 1] || 0;
      const longValue = longSMA[longSMA.length - 1] || 0;
      
      let crossover: SignalType = 'neutral';
      if (shortValue > longValue) {
        crossover = 'bullish';
      } else if (shortValue < longValue) {
        crossover = 'bearish';
      }
      
      return { short: shortValue, long: longValue, crossover };
    } catch (error) {
      logger.error('Error calculating SMA:', error);
      return { short: 0, long: 0, crossover: 'neutral' };
    }
  }

  /**
   * Calculate ATR (Average True Range)
   */
  private calculateATR(ohlcv: OHLCV[]): number {
    try {
      const high = ohlcv.map(candle => candle.high);
      const low = ohlcv.map(candle => candle.low);
      const close = ohlcv.map(candle => candle.close);
      
      const atrValues = ATR.calculate({
        high,
        low,
        close,
        period: SETTINGS.technicalIndicators.atr.period
      });
      
      return atrValues[atrValues.length - 1] || 0;
    } catch (error) {
      logger.error('Error calculating ATR:', error);
      return 0;
    }
  }

  /**
   * Analyze technical signals and determine overall signal
   */
  private analyzeSignals(indicators: TechnicalIndicators): {
    rsi: SignalType;
    macd: SignalType;
    bollingerBands: SignalType;
    sma: SignalType;
  } {
    const signals = {
      rsi: 'neutral' as SignalType,
      macd: 'neutral' as SignalType,
      bollingerBands: 'neutral' as SignalType,
      sma: indicators.sma.crossover
    };

    // RSI signals
    if (indicators.rsi < SETTINGS.technicalIndicators.rsi.oversold) {
      signals.rsi = 'bullish';
    } else if (indicators.rsi > SETTINGS.technicalIndicators.rsi.overbought) {
      signals.rsi = 'bearish';
    }

    // MACD signals
    if (indicators.macd.histogram > 0 && indicators.macd.macd > indicators.macd.signal) {
      signals.macd = 'bullish';
    } else if (indicators.macd.histogram < 0 && indicators.macd.macd < indicators.macd.signal) {
      signals.macd = 'bearish';
    }

    // Bollinger Bands signals (simplified - price relative to bands)
    const currentPrice = indicators.bollingerBands.middle; // Using middle as proxy for current price
    if (currentPrice < indicators.bollingerBands.lower) {
      signals.bollingerBands = 'bullish'; // Oversold
    } else if (currentPrice > indicators.bollingerBands.upper) {
      signals.bollingerBands = 'bearish'; // Overbought
    }

    return signals;
  }

  /**
   * Calculate all technical indicators for a given OHLCV data
   */
  public calculateIndicators(ohlcv: OHLCV[]): TechnicalIndicators {
    if (ohlcv.length < 50) {
      throw new Error('Insufficient data for technical analysis. Need at least 50 data points.');
    }

    const prices = ohlcv.map(candle => candle.close);
    
    const indicators: TechnicalIndicators = {
      rsi: this.calculateRSI(prices),
      macd: this.calculateMACD(prices),
      bollingerBands: this.calculateBollingerBands(prices),
      sma: this.calculateSMA(prices),
      atr: this.calculateATR(ohlcv),
      adx: this.calculateADX(ohlcv)
    };

    logger.info('Technical indicators calculated successfully', { indicators });
    return indicators;
  }

  /**
   * Get technical signals for decision making
   */
  public getTechnicalSignals(indicators: TechnicalIndicators) {
    return this.analyzeSignals(indicators);
  }

  /**
   * Calculate risk management levels based on ATR
   */
  public calculateRiskManagement(currentPrice: number, atr: number): {
    stopLoss: number;
    takeProfit: number;
    positionSize: number;
    maxRisk: number;
  } {
    const stopLoss = currentPrice - (atr * SETTINGS.atrMultiplier.sl);
    const takeProfit = currentPrice + (atr * SETTINGS.atrMultiplier.tp);
    const maxRisk = SETTINGS.maxRiskPerTrade;
    
    // Simplified position sizing (in practice, this would use portfolio value)
    const positionSize = 1000; // $1000 position size example
    
    return {
      stopLoss,
      takeProfit,
      positionSize,
      maxRisk
    };
  }

  /**
   * Calculate Average Directional Index (ADX)
   */
  private calculateADX(ohlcv: OHLCV[]): number {
    if (ohlcv.length < 14) return 0;

    const period = 14;
    const trueRanges: number[] = [];
    const plusDMs: number[] = [];
    const minusDMs: number[] = [];

    // Calculate True Range, +DM, and -DM
    for (let i = 1; i < ohlcv.length; i++) {
      const current = ohlcv[i];
      const previous = ohlcv[i - 1];

      // True Range
      const tr = Math.max(
        current.high - current.low,
        Math.abs(current.high - previous.close),
        Math.abs(current.low - previous.close)
      );
      trueRanges.push(tr);

      // Directional Movement
      const upMove = current.high - previous.high;
      const downMove = previous.low - current.low;

      const plusDM = upMove > downMove && upMove > 0 ? upMove : 0;
      const minusDM = downMove > upMove && downMove > 0 ? downMove : 0;

      plusDMs.push(plusDM);
      minusDMs.push(minusDM);
    }

    if (trueRanges.length < period) return 0;

    // Calculate smoothed averages
    let atr = trueRanges.slice(0, period).reduce((sum, tr) => sum + tr, 0) / period;
    let plusDI = plusDMs.slice(0, period).reduce((sum, dm) => sum + dm, 0) / period;
    let minusDI = minusDMs.slice(0, period).reduce((sum, dm) => sum + dm, 0) / period;

    // Calculate DI+ and DI-
    plusDI = (plusDI / atr) * 100;
    minusDI = (minusDI / atr) * 100;

    // Calculate DX
    const dx = Math.abs(plusDI - minusDI) / (plusDI + minusDI) * 100;

    // For simplicity, return DX as ADX approximation
    // In a full implementation, ADX would be a smoothed average of DX values
    return Math.round(dx * 100) / 100;
  }
}