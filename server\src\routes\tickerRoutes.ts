import { Router, Request, Response } from 'express';
import { TickerDataModel } from '../models/TickerData';
import { ApiResponse, DashboardData, TickerData } from '../types';
import { logger, logError, logPerformance, logAPIRequest } from '../utils/logger';
import yahooFinance from 'yahoo-finance2';

const router = Router();

/**
 * GET /api/tickers - Get all ticker data
 */
router.get('/', async (req: Request, res: Response) => {
  const startTime = Date.now();
  const operation = 'GET /api/tickers';

  try {
    logger.info('Fetching all ticker data', {
      component: 'API',
      operation,
      query: req.query
    });

    const tickers = await TickerDataModel.find().sort({ lastUpdated: -1 });
    const duration = Date.now() - startTime;

    logger.info('Successfully fetched ticker data', {
      component: 'API',
      operation,
      tickerCount: tickers.length,
      duration
    });


    const response: ApiResponse<TickerData[]> = {
      success: true,
      data: tickers,
      timestamp: new Date()
    };

    logAPIRequest('GET', '/api/tickers', 200, duration, { tickerCount: tickers.length });
    res.json(response);
  } catch (error) {
    const duration = Date.now() - startTime;
    logError('API', operation, error as Error, { duration });

    const response: ApiResponse<null> = {
      success: false,
      error: 'Failed to fetch ticker data',
      timestamp: new Date()
    };

    logAPIRequest('GET', '/api/tickers', 500, duration, { error: (error as Error).message });
    res.status(500).json(response);
  }
});

/**
 * GET /api/tickers/:ticker - Get specific ticker data
 */
router.get('/:ticker', async (req: Request, res: Response) => {
  const startTime = Date.now();
  const { ticker } = req.params;
  const operation = `GET /api/tickers/${ticker}`;

  try {
    logger.info('Fetching specific ticker data', {
      component: 'API',
      operation,
      ticker: ticker.toUpperCase(),
      query: req.query
    });

    const tickerData = await TickerDataModel.findOne({ ticker: ticker.toUpperCase() });
    const duration = Date.now() - startTime;

    if (!tickerData) {
      logger.warn('Ticker not found', {
        component: 'API',
        operation,
        ticker: ticker.toUpperCase(),
        duration
      });

      const response: ApiResponse<null> = {
        success: false,
        error: `Ticker ${ticker} not found`,
        timestamp: new Date()
      };

      logAPIRequest('GET', `/api/tickers/${ticker}`, 404, duration, { ticker: ticker.toUpperCase() });
      return res.status(404).json(response);
    }

    logger.info('Successfully fetched ticker data', {
      component: 'API',
      operation,
      ticker: ticker.toUpperCase(),
      duration,
      lastUpdated: tickerData.lastUpdated,
      currentPrice: tickerData.currentPrice,
      newsCount: tickerData.news.length
    });


    const response: ApiResponse<TickerData> = {
      success: true,
      data: tickerData,
      timestamp: new Date()
    };

    logAPIRequest('GET', `/api/tickers/${ticker}`, 200, duration, {
      ticker: ticker.toUpperCase(),
      newsCount: tickerData.news.length
    });
    return res.json(response);
  } catch (error) {
    const duration = Date.now() - startTime;
    logError('API', operation, error as Error, {
      ticker: ticker.toUpperCase(),
      duration
    });

    const response: ApiResponse<null> = {
      success: false,
      error: 'Failed to fetch ticker data',
      timestamp: new Date()
    };

    logAPIRequest('GET', `/api/tickers/${ticker}`, 500, duration, {
      ticker: ticker.toUpperCase(),
      error: (error as Error).message
    });
    return res.status(500).json(response);
  }
});

/**
 * GET /api/tickers/:ticker/history - Get historical data for a ticker
 */
router.get('/:ticker/history', async (req: Request, res: Response) => {
  try {
    const { ticker } = req.params;
    const today = new Date();
    const oneYearAgo = new Date(today.getFullYear() - 1, today.getMonth(), today.getDate());

    const history = await yahooFinance.historical(ticker.toUpperCase(), {
      period1: oneYearAgo.toISOString().split('T')[0],
      period2: today.toISOString().split('T')[0],
      interval: '1d',
    });

    const response: ApiResponse<any> = {
      success: true,
      data: history,
      timestamp: new Date(),
    };

    res.json(response);
  } catch (error) {
    logger.error(`Error fetching historical data for ${req.params.ticker}:`, error);
    const response: ApiResponse<null> = {
      success: false,
      error: 'Failed to fetch historical data',
      timestamp: new Date(),
    };
    res.status(500).json(response);
  }
});

/**
 * GET /api/tickers/dashboard/summary - Get dashboard summary
 */
router.get('/dashboard/summary', async (req: Request, res: Response) => {
  try {
    const tickers = await TickerDataModel.find().sort({ lastUpdated: -1 });

    const buySignals = tickers.filter(t => t.recommendation.action === 'Buy').length;
    const sellSignals = tickers.filter(t => t.recommendation.action === 'Sell').length;
    const holdSignals = tickers.filter(t => t.recommendation.action === 'Hold').length;

    // Calculate average sentiment
    const sentimentCounts = { Positive: 0, Neutral: 0, Negative: 0 };
    tickers.forEach(ticker => {
      sentimentCounts[ticker.recommendation.sentiment]++;
    });

    let averageSentiment: 'Positive' | 'Neutral' | 'Negative' = 'Neutral';
    if (sentimentCounts.Positive > sentimentCounts.Negative && sentimentCounts.Positive > sentimentCounts.Neutral) {
      averageSentiment = 'Positive';
    } else if (sentimentCounts.Negative > sentimentCounts.Positive && sentimentCounts.Negative > sentimentCounts.Neutral) {
      averageSentiment = 'Negative';
    }

    const dashboardData: DashboardData = {
      tickers,
      summary: {
        totalTickers: tickers.length,
        buySignals,
        sellSignals,
        holdSignals,
        averageSentiment
      },
      lastUpdate: new Date()
    };

    const response: ApiResponse<DashboardData> = {
      success: true,
      data: dashboardData,
      timestamp: new Date()
    };

    res.json(response);
  } catch (error) {
    logger.error('Error fetching dashboard summary:', error);
    const response: ApiResponse<null> = {
      success: false,
      error: 'Failed to fetch dashboard summary',
      timestamp: new Date()
    };
    res.status(500).json(response);
  }
});

/**
 * GET /api/tickers/:ticker/history - Get historical OHLCV data for a ticker
 */
router.get('/:ticker/history', async (req: Request, res: Response) => {
  const startTime = Date.now();
  const { ticker } = req.params;
  const operation = `GET /api/tickers/${ticker}/history`;

  try {
    logger.info('Fetching historical data', {
      component: 'API',
      operation,
      ticker: ticker.toUpperCase(),
      query: req.query
    });

    const { DataService } = await import('../services/DataService');
    const dataService = new DataService();

    const historyData = await dataService.fetchHistoricalData(ticker.toUpperCase());
    const duration = Date.now() - startTime;

    if (!historyData || historyData.length === 0) {
      logger.warn('No historical data available', {
        component: 'API',
        operation,
        ticker: ticker.toUpperCase(),
        duration
      });

      const response: ApiResponse<null> = {
        success: false,
        error: `No historical data available for ticker ${ticker}`,
        timestamp: new Date()
      };

      logAPIRequest('GET', `/api/tickers/${ticker}/history`, 404, duration, {
        ticker: ticker.toUpperCase()
      });
      return res.status(404).json(response);
    }

    logger.info('Successfully fetched historical data', {
      component: 'API',
      operation,
      ticker: ticker.toUpperCase(),
      duration,
      recordCount: historyData.length,
      dateRange: {
        from: historyData[0]?.timestamp,
        to: historyData[historyData.length - 1]?.timestamp
      }
    });


    const response: ApiResponse<any[]> = {
      success: true,
      data: historyData,
      timestamp: new Date()
    };

    logAPIRequest('GET', `/api/tickers/${ticker}/history`, 200, duration, {
      ticker: ticker.toUpperCase(),
      recordCount: historyData.length
    });
    return res.json(response);
  } catch (error) {
    const duration = Date.now() - startTime;
    logError('API', operation, error as Error, {
      ticker: ticker.toUpperCase(),
      duration
    });

    const response: ApiResponse<null> = {
      success: false,
      error: 'Failed to fetch historical data',
      timestamp: new Date()
    };

    logAPIRequest('GET', `/api/tickers/${ticker}/history`, 500, duration, {
      ticker: ticker.toUpperCase(),
      error: (error as Error).message
    });
    return res.status(500).json(response);
  }
});

export default router;