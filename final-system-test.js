// Final Comprehensive System Test
const baseUrl = 'http://localhost:3001/api';

async function testCompleteDataFlow() {
  console.log('🔄 Testing Complete Data Flow');
  console.log('=' .repeat(50));
  
  try {
    // Step 1: Get all tickers
    console.log('\n1️⃣ Fetching ticker list...');
    const tickersResponse = await fetch(`${baseUrl}/tickers`);
    const tickersData = await tickersResponse.json();
    
    if (!tickersData.success || tickersData.data.length === 0) {
      console.log('❌ No tickers available');
      return false;
    }
    
    const ticker = tickersData.data[0];
    console.log(`✅ Found ${tickersData.data.length} tickers, testing with ${ticker.ticker}`);
    
    // Step 2: Get specific ticker details
    console.log('\n2️⃣ Fetching ticker details...');
    const detailResponse = await fetch(`${baseUrl}/tickers/${ticker.ticker}`);
    const detailData = await detailResponse.json();
    
    if (detailData.success) {
      console.log(`✅ Ticker details retrieved for ${ticker.ticker}`);
      console.log(`   💰 Price: $${detailData.data.currentPrice}`);
      console.log(`   📊 Action: ${detailData.data.recommendation.action}`);
      console.log(`   💭 Sentiment: ${detailData.data.recommendation.sentiment}`);
    } else {
      console.log(`❌ Failed to get ticker details: ${detailData.error}`);
    }
    
    // Step 3: Get historical data
    console.log('\n3️⃣ Fetching historical data...');
    const historyResponse = await fetch(`${baseUrl}/tickers/${ticker.ticker}/history`);
    const historyData = await historyResponse.json();
    
    if (historyData.success) {
      console.log(`✅ Historical data retrieved: ${historyData.data.length} data points`);
      const latest = historyData.data[0];
      console.log(`   📅 Latest: ${new Date(latest.date).toDateString()}`);
      console.log(`   💰 OHLC: O=${latest.open} H=${latest.high} L=${latest.low} C=${latest.close}`);
    } else {
      console.log(`❌ Failed to get historical data: ${historyData.error}`);
    }
    
    // Step 4: Get dashboard summary
    console.log('\n4️⃣ Fetching dashboard summary...');
    const dashboardResponse = await fetch(`${baseUrl}/tickers/dashboard/summary`);
    const dashboardData = await dashboardResponse.json();
    
    if (dashboardData.success) {
      const summary = dashboardData.data.summary;
      console.log(`✅ Dashboard summary retrieved`);
      console.log(`   📊 Total tickers: ${summary.totalTickers}`);
      console.log(`   📈 Signals: ${summary.buySignals} buy, ${summary.sellSignals} sell, ${summary.holdSignals} hold`);
      console.log(`   💭 Average sentiment: ${summary.averageSentiment}`);
    } else {
      console.log(`❌ Failed to get dashboard summary: ${dashboardData.error}`);
    }
    
    return true;
  } catch (error) {
    console.log(`💥 Data flow test failed: ${error.message}`);
    return false;
  }
}

async function testTechnicalAnalysisAccuracy() {
  console.log('\n🧮 Testing Technical Analysis Accuracy');
  console.log('=' .repeat(50));
  
  try {
    const response = await fetch(`${baseUrl}/tickers`);
    const data = await response.json();
    
    if (!data.success || data.data.length === 0) {
      console.log('❌ No data available for testing');
      return false;
    }
    
    const ticker = data.data[0];
    const indicators = ticker.technicalIndicators;
    
    console.log(`\n📊 Analyzing ${ticker.ticker} Technical Indicators:`);
    
    let validationsPassed = 0;
    let totalValidations = 0;
    
    // RSI validation (0-100 range)
    totalValidations++;
    if (indicators.rsi >= 0 && indicators.rsi <= 100) {
      console.log(`✅ RSI: ${indicators.rsi.toFixed(2)} (valid range)`);
      validationsPassed++;
    } else {
      console.log(`❌ RSI: ${indicators.rsi.toFixed(2)} (invalid range)`);
    }
    
    // ATR validation (positive value)
    totalValidations++;
    if (indicators.atr > 0) {
      console.log(`✅ ATR: ${indicators.atr.toFixed(2)} (positive)`);
      validationsPassed++;
    } else {
      console.log(`❌ ATR: ${indicators.atr.toFixed(2)} (should be positive)`);
    }
    
    // Bollinger Bands validation (proper ordering)
    totalValidations++;
    const bb = indicators.bollingerBands;
    if (bb.lower < bb.middle && bb.middle < bb.upper) {
      console.log(`✅ Bollinger Bands: ${bb.lower.toFixed(2)} < ${bb.middle.toFixed(2)} < ${bb.upper.toFixed(2)}`);
      validationsPassed++;
    } else {
      console.log(`❌ Bollinger Bands: Invalid ordering`);
    }
    
    // MACD validation (reasonable values)
    totalValidations++;
    const macd = indicators.macd;
    if (typeof macd.macd === 'number' && typeof macd.signal === 'number' && typeof macd.histogram === 'number') {
      console.log(`✅ MACD: Line=${macd.macd.toFixed(4)}, Signal=${macd.signal.toFixed(4)}, Hist=${macd.histogram.toFixed(4)}`);
      validationsPassed++;
    } else {
      console.log(`❌ MACD: Invalid values`);
    }
    
    // SMA validation
    totalValidations++;
    const sma = indicators.sma;
    if (sma.short > 0 && sma.long > 0 && ['bullish', 'bearish'].includes(sma.crossover)) {
      console.log(`✅ SMA: Short=${sma.short.toFixed(2)}, Long=${sma.long.toFixed(2)}, Crossover=${sma.crossover}`);
      validationsPassed++;
    } else {
      console.log(`❌ SMA: Invalid values`);
    }
    
    // ADX validation (if present)
    if (indicators.adx !== undefined) {
      totalValidations++;
      if (indicators.adx >= 0 && indicators.adx <= 100) {
        console.log(`✅ ADX: ${indicators.adx.toFixed(2)} (valid range)`);
        validationsPassed++;
      } else {
        console.log(`❌ ADX: ${indicators.adx.toFixed(2)} (invalid range)`);
      }
    } else {
      console.log(`ℹ️  ADX: Not present in current data (expected for older records)`);
    }
    
    const accuracy = (validationsPassed / totalValidations) * 100;
    console.log(`\n📊 Technical Analysis Accuracy: ${validationsPassed}/${totalValidations} (${accuracy.toFixed(1)}%)`);
    
    return accuracy >= 80; // 80% accuracy threshold
  } catch (error) {
    console.log(`💥 Technical analysis test failed: ${error.message}`);
    return false;
  }
}

async function testSystemPerformance() {
  console.log('\n⚡ Testing System Performance');
  console.log('=' .repeat(50));
  
  const endpoints = [
    '/tickers',
    '/tickers/dashboard/summary',
    '/logs?limit=5',
    '/discovery/stats'
  ];
  
  const results = [];
  
  for (const endpoint of endpoints) {
    try {
      const startTime = Date.now();
      const response = await fetch(`${baseUrl}${endpoint}`);
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      const success = response.ok;
      results.push({ endpoint, responseTime, success });
      
      console.log(`${success ? '✅' : '❌'} ${endpoint}: ${responseTime}ms`);
    } catch (error) {
      console.log(`❌ ${endpoint}: Error - ${error.message}`);
      results.push({ endpoint, responseTime: -1, success: false });
    }
  }
  
  const successfulRequests = results.filter(r => r.success);
  const averageResponseTime = successfulRequests.reduce((sum, r) => sum + r.responseTime, 0) / successfulRequests.length;
  
  console.log(`\n📊 Performance Summary:`);
  console.log(`   ✅ Successful requests: ${successfulRequests.length}/${results.length}`);
  console.log(`   ⚡ Average response time: ${averageResponseTime.toFixed(0)}ms`);
  
  return successfulRequests.length === results.length && averageResponseTime < 1000;
}

async function testErrorHandling() {
  console.log('\n🛡️  Testing Error Handling');
  console.log('=' .repeat(50));
  
  const errorTests = [
    { endpoint: '/tickers/INVALID_TICKER', expectedStatus: 404, description: 'Invalid ticker' },
    { endpoint: '/tickers/dashboard/invalid', expectedStatus: 404, description: 'Invalid dashboard endpoint' },
    { endpoint: '/invalid/endpoint', expectedStatus: 404, description: 'Non-existent endpoint' }
  ];
  
  let passedTests = 0;
  
  for (const test of errorTests) {
    try {
      const response = await fetch(`${baseUrl}${test.endpoint}`);
      const data = await response.json();
      
      if (response.status === test.expectedStatus && !data.success) {
        console.log(`✅ ${test.description}: Correctly returned ${response.status}`);
        passedTests++;
      } else {
        console.log(`❌ ${test.description}: Expected ${test.expectedStatus}, got ${response.status}`);
      }
    } catch (error) {
      console.log(`❌ ${test.description}: Network error - ${error.message}`);
    }
  }
  
  console.log(`\n📊 Error Handling: ${passedTests}/${errorTests.length} tests passed`);
  return passedTests === errorTests.length;
}

async function runFinalSystemTest() {
  console.log('🏁 Final Comprehensive System Test');
  console.log('=' .repeat(60));
  
  const testResults = {
    dataFlow: false,
    technicalAnalysis: false,
    performance: false,
    errorHandling: false
  };
  
  // Run all tests
  testResults.dataFlow = await testCompleteDataFlow();
  testResults.technicalAnalysis = await testTechnicalAnalysisAccuracy();
  testResults.performance = await testSystemPerformance();
  testResults.errorHandling = await testErrorHandling();
  
  // Final summary
  console.log('\n🎯 Final Test Results');
  console.log('=' .repeat(40));
  
  const passedTests = Object.values(testResults).filter(result => result).length;
  const totalTests = Object.keys(testResults).length;
  
  Object.entries(testResults).forEach(([test, passed]) => {
    console.log(`${passed ? '✅' : '❌'} ${test.charAt(0).toUpperCase() + test.slice(1).replace(/([A-Z])/g, ' $1')}: ${passed ? 'PASSED' : 'FAILED'}`);
  });
  
  const overallSuccess = passedTests === totalTests;
  console.log(`\n🏆 Overall System Status: ${overallSuccess ? '✅ ALL TESTS PASSED' : `❌ ${passedTests}/${totalTests} TESTS PASSED`}`);
  
  if (overallSuccess) {
    console.log('\n🎉 Trading Bot System is fully operational and ready for production!');
  } else {
    console.log('\n⚠️  Some issues detected. Please review failed tests.');
  }
  
  return overallSuccess;
}

// Run the final comprehensive test
runFinalSystemTest().catch(console.error);
