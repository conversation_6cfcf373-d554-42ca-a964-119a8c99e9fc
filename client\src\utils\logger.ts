/**
 * Client-side logging utility for debugging and monitoring
 */

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3
}

interface LogEntry {
  timestamp: string;
  level: LogLevel;
  component: string;
  operation?: string;
  message: string;
  data?: any;
  duration?: number;
  error?: string;
}

class ClientLogger {
  private logLevel: LogLevel;
  private logs: LogEntry[] = [];
  private maxLogs = 1000;

  constructor() {
    this.logLevel = process.env.NODE_ENV === 'development' ? LogLevel.DEBUG : LogLevel.INFO;
  }

  private shouldLog(level: LogLevel): boolean {
    return level >= this.logLevel;
  }

  private createLogEntry(
    level: LogLevel,
    component: string,
    message: string,
    operation?: string,
    data?: any,
    duration?: number,
    error?: string
  ): LogEntry {
    return {
      timestamp: new Date().toISOString(),
      level,
      component,
      operation,
      message,
      data,
      duration,
      error
    };
  }

  private addLog(entry: LogEntry): void {
    this.logs.push(entry);
    
    // Keep only the most recent logs
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }

    // Console output in development
    if (process.env.NODE_ENV === 'development') {
      const logMethod = this.getConsoleMethod(entry.level);
      const prefix = `[${entry.timestamp}] ${LogLevel[entry.level]} [${entry.component}]`;
      const suffix = entry.operation ? ` [${entry.operation}]` : '';
      const duration = entry.duration ? ` (${entry.duration}ms)` : '';
      
      logMethod(`${prefix}${suffix}${duration}: ${entry.message}`, entry.data || '');
    }
  }

  private getConsoleMethod(level: LogLevel): (...args: any[]) => void {
    switch (level) {
      case LogLevel.DEBUG:
        return console.debug;
      case LogLevel.INFO:
        return console.info;
      case LogLevel.WARN:
        return console.warn;
      case LogLevel.ERROR:
        return console.error;
      default:
        return console.log;
    }
  }

  debug(component: string, message: string, operation?: string, data?: any): void {
    if (this.shouldLog(LogLevel.DEBUG)) {
      this.addLog(this.createLogEntry(LogLevel.DEBUG, component, message, operation, data));
    }
  }

  info(component: string, message: string, operation?: string, data?: any): void {
    if (this.shouldLog(LogLevel.INFO)) {
      this.addLog(this.createLogEntry(LogLevel.INFO, component, message, operation, data));
    }
  }

  warn(component: string, message: string, operation?: string, data?: any): void {
    if (this.shouldLog(LogLevel.WARN)) {
      this.addLog(this.createLogEntry(LogLevel.WARN, component, message, operation, data));
    }
  }

  error(component: string, message: string, operation?: string, error?: Error, data?: any): void {
    if (this.shouldLog(LogLevel.ERROR)) {
      this.addLog(this.createLogEntry(
        LogLevel.ERROR, 
        component, 
        message, 
        operation, 
        data, 
        undefined, 
        error?.message
      ));
    }
  }

  // Performance logging
  performance(component: string, operation: string, duration: number, data?: any): void {
    const level = duration > 5000 ? LogLevel.WARN : duration > 2000 ? LogLevel.INFO : LogLevel.DEBUG;
    const message = `Performance: ${operation} took ${duration}ms`;
    
    if (this.shouldLog(level)) {
      this.addLog(this.createLogEntry(level, component, message, operation, data, duration));
    }
  }

  // API call logging
  apiCall(method: string, url: string, duration: number, status: number, data?: any): void {
    const operation = `${method} ${url}`;
    const message = `API ${operation} - ${status} (${duration}ms)`;
    const level = status >= 400 ? LogLevel.ERROR : status >= 300 ? LogLevel.WARN : LogLevel.INFO;
    
    if (this.shouldLog(level)) {
      this.addLog(this.createLogEntry(level, 'API', message, operation, { ...data, status }, duration));
    }
  }

  // Chart logging
  chartEvent(operation: string, ticker: string, duration?: number, data?: any): void {
    const message = `Chart ${operation} for ${ticker}`;
    
    if (this.shouldLog(LogLevel.DEBUG)) {
      this.addLog(this.createLogEntry(LogLevel.DEBUG, 'Chart', message, operation, { ticker, ...data }, duration));
    }
  }

  // Data flow logging
  dataFlow(component: string, operation: string, dataType: string, count?: number, duration?: number): void {
    const message = `Data flow: ${operation} - ${dataType}${count ? ` (${count} items)` : ''}`;
    
    if (this.shouldLog(LogLevel.DEBUG)) {
      this.addLog(this.createLogEntry(LogLevel.DEBUG, component, message, operation, { dataType, count }, duration));
    }
  }

  // Get logs for debugging
  getLogs(level?: LogLevel, component?: string): LogEntry[] {
    return this.logs.filter(log => {
      if (level !== undefined && log.level < level) return false;
      if (component && log.component !== component) return false;
      return true;
    });
  }

  // Clear logs
  clearLogs(): void {
    this.logs = [];
  }

  // Export logs for debugging
  exportLogs(): string {
    return JSON.stringify(this.logs, null, 2);
  }
}

// Create singleton instance
export const logger = new ClientLogger();

// Helper functions for common logging patterns
export const logApiCall = (method: string, url: string, duration: number, status: number, data?: any) => {
  logger.apiCall(method, url, duration, status, data);
};

export const logPerformance = (component: string, operation: string, duration: number, data?: any) => {
  logger.performance(component, operation, duration, data);
};

export const logError = (component: string, operation: string, error: Error, data?: any) => {
  logger.error(component, `Error in ${operation}: ${error.message}`, operation, error, data);
};

export const logDataFlow = (component: string, operation: string, dataType: string, count?: number, duration?: number) => {
  logger.dataFlow(component, operation, dataType, count, duration);
};

export const logChartEvent = (operation: string, ticker: string, duration?: number, data?: any) => {
  logger.chartEvent(operation, ticker, duration, data);
};

// Performance measurement helper
export const measurePerformance = <T>(
  component: string,
  operation: string,
  fn: () => T | Promise<T>,
  data?: any
): T | Promise<T> => {
  const startTime = performance.now();
  
  try {
    const result = fn();
    
    if (result instanceof Promise) {
      return result.then(
        (value) => {
          const duration = performance.now() - startTime;
          logPerformance(component, operation, duration, data);
          return value;
        },
        (error) => {
          const duration = performance.now() - startTime;
          logError(component, operation, error, { ...data, duration });
          throw error;
        }
      );
    } else {
      const duration = performance.now() - startTime;
      logPerformance(component, operation, duration, data);
      return result;
    }
  } catch (error) {
    const duration = performance.now() - startTime;
    logError(component, operation, error as Error, { ...data, duration });
    throw error;
  }
};
