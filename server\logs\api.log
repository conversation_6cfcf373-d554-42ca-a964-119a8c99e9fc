[2025-06-23 09:56:37.110] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"LMT","sentiment":"Neutral","confidence":"High"}
[2025-06-23 09:56:45.210] [INFO] [API] - GET /api/tickers: Fetching all ticker data | {"service":"trading-bot","query":{}}
[2025-06-23 09:56:45.222] [INFO] [API] - GET /api/tickers: Successfully fetched ticker data (12ms) | {"service":"trading-bot","tickerCount":9}
[2025-06-23 09:56:45.224] [INFO] [API]: GET /api/tickers - Status 200 (12ms) | {"service":"trading-bot","tickerCount":9}
[2025-06-23 09:56:52.935] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"LMT","sentiment":"Neutral","confidence":"High"}
[2025-06-23 09:57:06.955] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"LMT","sentiment":"Neutral","confidence":"High"}
[2025-06-23 09:57:25.257] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"LMT","sentiment":"Positive","confidence":"Medium"}
[2025-06-23 09:57:40.175] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"LMT","sentiment":"Neutral","confidence":"High"}
[2025-06-23 09:57:40.176] [INFO] [General]: Generated recommendation for LMT | {"service":"trading-bot","recommendation":{"ticker":"LMT","action":"Hold","confidence":"Medium","technicalSignals":{"rsi":"neutral","macd":"bearish","bollingerBands":"neutral","sma":"bullish"},"sentiment":"Neutral","reasoning":"Neutral signals, holding position.","riskManagement":{"stopLoss":445.43160668540713,"takeProfit":520.8167866291857,"positionSize":1000,"maxRisk":0.02},"timestamp":"2025-06-23T06:57:40.176Z"}}
[2025-06-23 09:57:40.227] [INFO] [DataService] - fetchHistoricalData: Starting historical data fetch | {"service":"trading-bot","ticker":"RTX","startDate":"2023-01-01"}
[2025-06-23 09:57:40.765] [INFO] [DataService] - fetchHistoricalData: Successfully fetched historical data (537ms) | {"service":"trading-bot","ticker":"RTX","recordCount":618,"dateRange":{"from":"2023-01-03T14:30:00.000Z","to":"2025-06-20T13:30:00.000Z"}}
[2025-06-23 09:57:40.766] [INFO] [DataService] - fetchCurrentPrice: Starting current price fetch | {"service":"trading-bot","ticker":"RTX"}
[2025-06-23 09:57:40.925] [INFO] [DataService] - fetchCurrentPrice: Successfully fetched current price (159ms) | {"service":"trading-bot","ticker":"RTX","currentPrice":146.64,"marketState":"PREPRE","lastUpdated":"2025-06-20T20:00:02.000Z"}
[2025-06-23 09:57:40.943] [INFO] [General]: Technical indicators calculated successfully | {"service":"trading-bot","indicators":{"rsi":67.55,"macd":{"macd":3.9098971743455877,"signal":3.3212177561387213,"histogram":0.5886794182068664},"bollingerBands":{"upper":149.21649038181047,"middle":139.59250183105468,"lower":129.96851328029888},"sma":{"short":139.59250183105468,"long":132.94280029296874,"crossover":"bullish"},"atr":3.1647760785687544,"adx":3.23}}
[2025-06-23 09:57:41.901] [INFO] [General]: Fetched 20 recent news articles for RTX | {"service":"trading-bot"}
[2025-06-23 09:57:45.218] [INFO] [API] - GET /api/tickers: Fetching all ticker data | {"service":"trading-bot","query":{}}
[2025-06-23 09:57:45.231] [INFO] [API] - GET /api/tickers: Successfully fetched ticker data (13ms) | {"service":"trading-bot","tickerCount":9}
[2025-06-23 09:57:45.233] [INFO] [API]: GET /api/tickers - Status 200 (13ms) | {"service":"trading-bot","tickerCount":9}
[2025-06-23 09:57:53.343] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Neutral","confidence":"High"}
[2025-06-23 09:58:02.187] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Neutral","confidence":"High"}
[2025-06-23 09:58:10.856] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Neutral","confidence":"High"}
[2025-06-23 09:58:18.748] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Neutral","confidence":"High"}
[2025-06-23 09:58:28.882] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Neutral","confidence":"High"}
[2025-06-23 09:58:36.000] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Neutral","confidence":"High"}
[2025-06-23 09:58:45.214] [INFO] [API] - GET /api/tickers: Fetching all ticker data | {"service":"trading-bot","query":{}}
[2025-06-23 09:58:45.225] [INFO] [API] - GET /api/tickers: Successfully fetched ticker data (11ms) | {"service":"trading-bot","tickerCount":9}
[2025-06-23 09:58:45.226] [INFO] [API]: GET /api/tickers - Status 200 (11ms) | {"service":"trading-bot","tickerCount":9}
[2025-06-23 09:58:46.816] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Neutral","confidence":"High"}
[2025-06-23 09:58:59.388] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Positive","confidence":"Medium"}
[2025-06-23 09:59:09.355] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Neutral","confidence":"High"}
[2025-06-23 09:59:20.957] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Neutral","confidence":"High"}
[2025-06-23 09:59:26.410] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Neutral","confidence":"High"}
[2025-06-23 09:59:34.781] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Neutral","confidence":"High"}
[2025-06-23 09:59:39.730] [INFO] [API] - GET /api/tickers: Fetching all ticker data | {"service":"trading-bot","query":{}}
[2025-06-23 09:59:39.743] [INFO] [API] - GET /api/tickers: Successfully fetched ticker data (13ms) | {"service":"trading-bot","tickerCount":9}
[2025-06-23 09:59:39.744] [INFO] [API]: GET /api/tickers - Status 200 (13ms) | {"service":"trading-bot","tickerCount":9}
[2025-06-23 09:59:45.215] [INFO] [API] - GET /api/tickers: Fetching all ticker data | {"service":"trading-bot","query":{}}
[2025-06-23 09:59:45.223] [INFO] [API] - GET /api/tickers: Successfully fetched ticker data (8ms) | {"service":"trading-bot","tickerCount":9}
[2025-06-23 09:59:45.224] [INFO] [API]: GET /api/tickers - Status 200 (8ms) | {"service":"trading-bot","tickerCount":9}
[2025-06-23 09:59:49.546] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Positive","confidence":"High"}
[2025-06-23 10:00:00.712] [INFO] [DataService] - fetchHistoricalData: Starting historical data fetch | {"service":"trading-bot","ticker":"LMT","startDate":"2023-01-01"}
[2025-06-23 10:00:01.140] [INFO] [DataService] - fetchHistoricalData: Successfully fetched historical data (427ms) | {"service":"trading-bot","ticker":"LMT","recordCount":618,"dateRange":{"from":"2023-01-03T14:30:00.000Z","to":"2025-06-20T13:30:00.000Z"}}
[2025-06-23 10:00:01.141] [INFO] [DataService] - fetchCurrentPrice: Starting current price fetch | {"service":"trading-bot","ticker":"LMT"}
[2025-06-23 10:00:01.237] [INFO] [DataService] - fetchCurrentPrice: Successfully fetched current price (96ms) | {"service":"trading-bot","ticker":"LMT","currentPrice":470.56,"marketState":"PREPRE","lastUpdated":"2025-06-20T20:00:02.000Z"}
[2025-06-23 10:00:01.246] [INFO] [General]: Technical indicators calculated successfully | {"service":"trading-bot","indicators":{"rsi":48.85,"macd":{"macd":0.5045616007407716,"signal":1.5152854012870949,"histogram":-1.0107238005463233},"bollingerBands":{"upper":488.88628348940074,"middle":474.93800201416013,"lower":460.9897205389195},"sma":{"short":474.93800201416013,"long":471.76919921875,"crossover":"bullish"},"atr":12.56419665729643,"adx":39.57}}
[2025-06-23 10:00:02.055] [INFO] [General]: Fetched 5 recent news articles for LMT | {"service":"trading-bot"}
[2025-06-23 10:00:04.402] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Positive","confidence":"Medium"}
[2025-06-23 10:00:17.199] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"LMT","sentiment":"Neutral","confidence":"Low"}
[2025-06-23 10:00:19.154] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Positive","confidence":"Medium"}
[2025-06-23 10:00:30.709] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Neutral","confidence":"High"}
[2025-06-23 10:00:34.915] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"LMT","sentiment":"Neutral","confidence":"Medium"}
[2025-06-23 10:00:41.970] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"RTX","sentiment":"Neutral","confidence":"High"}
[2025-06-23 10:00:45.222] [INFO] [API] - GET /api/tickers: Fetching all ticker data | {"service":"trading-bot","query":{}}
[2025-06-23 10:00:45.243] [INFO] [API] - GET /api/tickers: Successfully fetched ticker data (21ms) | {"service":"trading-bot","tickerCount":9}
[2025-06-23 10:00:45.244] [INFO] [API]: GET /api/tickers - Status 200 (21ms) | {"service":"trading-bot","tickerCount":9}
[2025-06-23 10:00:47.681] [INFO] [General]: Sentiment analysis completed | {"service":"trading-bot","ticker":"LMT","sentiment":"Neutral","confidence":"High"}
[2025-06-23 10:01:31.483] [INFO] [General]: Initialized default tickers | {"service":"trading-bot","count":6}
[2025-06-23 10:01:31.496] [WARN] [General]: No OpenAI API key provided for embeddings, using fallback similarity methods | {"service":"trading-bot"}
[2025-06-23 10:01:31.500] [INFO] [General]: Multi-model ensemble initialized with 3 models | {"service":"trading-bot"}
[2025-06-23 10:01:31.502] [INFO] [General]: Initialized 5 test cases for discovery validation | {"service":"trading-bot"}
[2025-06-23 10:01:31.654] [INFO] [General]: Connected to MongoDB successfully | {"service":"trading-bot"}
[2025-06-23 10:01:31.660] [INFO] [General]: Server running on port 3002 | {"service":"trading-bot"}
[2025-06-23 10:01:31.662] [INFO] [General]: Environment: development | {"service":"trading-bot"}
[2025-06-23 10:01:31.713] [INFO] [General]: Data update job started | {"service":"trading-bot"}
[2025-06-23 10:01:45.214] [INFO] [API] - GET /api/tickers: Fetching all ticker data | {"service":"trading-bot","query":{}}
[2025-06-23 10:01:45.283] [INFO] [API] - GET /api/tickers: Successfully fetched ticker data (69ms) | {"service":"trading-bot","tickerCount":9}
[2025-06-23 10:01:45.284] [INFO] [API]: GET /api/tickers - Status 200 (69ms) | {"service":"trading-bot","tickerCount":9}
[2025-06-23 10:03:58.169] [INFO] [General]: Initialized default tickers | {"service":"trading-bot","count":6}
[2025-06-23 10:03:58.176] [WARN] [General]: No OpenAI API key provided for embeddings, using fallback similarity methods | {"service":"trading-bot"}
[2025-06-23 10:03:58.177] [INFO] [General]: Multi-model ensemble initialized with 3 models | {"service":"trading-bot"}
[2025-06-23 10:03:58.178] [INFO] [General]: Initialized 5 test cases for discovery validation | {"service":"trading-bot"}
[2025-06-23 10:03:58.291] [INFO] [General]: Connected to MongoDB successfully | {"service":"trading-bot"}
[2025-06-23 10:03:58.302] [INFO] [General]: Server running on port 3002 | {"service":"trading-bot"}
[2025-06-23 10:03:58.303] [INFO] [General]: Environment: development | {"service":"trading-bot"}
[2025-06-23 10:03:58.364] [INFO] [General]: Data update job started | {"service":"trading-bot"}
[2025-06-23 10:04:45.241] [INFO] [API] - GET /api/tickers: Fetching all ticker data | {"service":"trading-bot","query":{}}
[2025-06-23 10:04:45.291] [INFO] [API] - GET /api/tickers: Successfully fetched ticker data (50ms) | {"service":"trading-bot","tickerCount":3}
[2025-06-23 10:04:45.293] [INFO] [API]: GET /api/tickers - Status 200 (50ms) | {"service":"trading-bot","tickerCount":3}
[2025-06-23 10:04:58.506] [INFO] [API] - GET /api/tickers/test: Fetching specific ticker data | {"service":"trading-bot","ticker":"TEST","query":{}}
[2025-06-23 10:04:58.515] [WARN] [API] - GET /api/tickers/test: Ticker not found (9ms) | {"service":"trading-bot","ticker":"TEST"}
[2025-06-23 10:04:58.516] [INFO] [API]: GET /api/tickers/test - Status 404 (9ms) | {"service":"trading-bot","ticker":"TEST"}
[2025-06-23 10:06:19.586] [INFO] [General]: Initialized default tickers | {"service":"trading-bot","count":6}
[2025-06-23 10:06:19.595] [WARN] [General]: No OpenAI API key provided for embeddings, using fallback similarity methods | {"service":"trading-bot"}
[2025-06-23 10:06:19.596] [INFO] [General]: Multi-model ensemble initialized with 3 models | {"service":"trading-bot"}
[2025-06-23 10:06:19.600] [INFO] [General]: Initialized 5 test cases for discovery validation | {"service":"trading-bot"}
[2025-06-23 10:06:19.792] [INFO] [General]: Connected to MongoDB successfully | {"service":"trading-bot"}
[2025-06-23 10:06:19.802] [INFO] [General]: Server running on port 3002 | {"service":"trading-bot"}
[2025-06-23 10:06:19.803] [INFO] [General]: Environment: development | {"service":"trading-bot"}
[2025-06-23 10:06:19.871] [INFO] [General]: Data update job started | {"service":"trading-bot"}
[2025-06-23 10:06:45.239] [INFO] [API] - GET /api/tickers: Fetching all ticker data | {"service":"trading-bot","query":{}}
[2025-06-23 10:06:45.299] [INFO] [API] - GET /api/tickers: Successfully fetched ticker data (60ms) | {"service":"trading-bot","tickerCount":3}
[2025-06-23 10:06:45.301] [INFO] [API]: GET /api/tickers - Status 200 (60ms) | {"service":"trading-bot","tickerCount":3}
[2025-06-23 10:06:53.921] [INFO] [API] - GET /api/tickers: Fetching all ticker data | {"service":"trading-bot","query":{}}
[2025-06-23 10:06:53.934] [INFO] [API] - GET /api/tickers: Successfully fetched ticker data (13ms) | {"service":"trading-bot","tickerCount":3}
[2025-06-23 10:06:53.935] [INFO] [API]: GET /api/tickers - Status 200 (13ms) | {"service":"trading-bot","tickerCount":3}
[2025-06-23 10:07:37.115] [INFO] [API] - GET /api/tickers: Fetching all ticker data | {"service":"trading-bot","query":{}}
[2025-06-23 10:07:37.142] [INFO] [API] - GET /api/tickers: Successfully fetched ticker data (27ms) | {"service":"trading-bot","tickerCount":3}
[2025-06-23 10:07:37.144] [INFO] [API]: GET /api/tickers - Status 200 (27ms) | {"service":"trading-bot","tickerCount":3}
[2025-06-23 10:07:37.266] [INFO] [API] - GET /api/tickers: Fetching all ticker data | {"service":"trading-bot","query":{}}
[2025-06-23 10:07:37.292] [INFO] [API] - GET /api/tickers: Successfully fetched ticker data (26ms) | {"service":"trading-bot","tickerCount":3}
[2025-06-23 10:07:37.295] [INFO] [API]: GET /api/tickers - Status 200 (26ms) | {"service":"trading-bot","tickerCount":3}
[2025-06-23 10:07:45.266] [INFO] [API] - GET /api/tickers: Fetching all ticker data | {"service":"trading-bot","query":{}}
[2025-06-23 10:07:45.295] [INFO] [API] - GET /api/tickers: Successfully fetched ticker data (29ms) | {"service":"trading-bot","tickerCount":3}
[2025-06-23 10:07:45.297] [INFO] [API]: GET /api/tickers - Status 200 (29ms) | {"service":"trading-bot","tickerCount":3}
[2025-06-23 10:08:21.329] [INFO] [API] - GET /api/tickers/XOM: Fetching specific ticker data | {"service":"trading-bot","ticker":"XOM","query":{}}
[2025-06-23 10:08:21.407] [INFO] [API] - GET /api/tickers/XOM: Successfully fetched ticker data (78ms) | {"service":"trading-bot","ticker":"XOM","lastUpdated":"2025-06-23T07:02:30.558Z","currentPrice":114.7,"newsCount":1}
[2025-06-23 10:08:21.408] [INFO] [API]: GET /api/tickers/XOM - Status 200 (78ms) | {"service":"trading-bot","ticker":"XOM","newsCount":1}
[2025-06-23 10:08:29.691] [INFO] [API] - GET /api/tickers: Fetching all ticker data | {"service":"trading-bot","query":{}}
[2025-06-23 10:08:29.697] [INFO] [API] - GET /api/tickers: Successfully fetched ticker data (6ms) | {"service":"trading-bot","tickerCount":3}
[2025-06-23 10:08:29.700] [INFO] [API]: GET /api/tickers - Status 200 (6ms) | {"service":"trading-bot","tickerCount":3}
[2025-06-23 10:08:29.706] [INFO] [API] - GET /api/tickers: Fetching all ticker data | {"service":"trading-bot","query":{}}
[2025-06-23 10:08:29.712] [INFO] [API] - GET /api/tickers: Successfully fetched ticker data (7ms) | {"service":"trading-bot","tickerCount":3}
[2025-06-23 10:08:29.713] [INFO] [API]: GET /api/tickers - Status 200 (7ms) | {"service":"trading-bot","tickerCount":3}
