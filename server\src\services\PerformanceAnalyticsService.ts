import { BacktestTrade, BacktestResults } from './BacktestingService';
import { logger } from '../utils/logger';

export interface PerformanceMetrics {
  totalReturn: number;
  annualizedReturn: number;
  volatility: number;
  sharpeRatio: number;
  sortinoRatio: number;
  calmarRatio: number;
  maxDrawdown: number;
  winRate: number;
  profitFactor: number;
  averageWin: number;
  averageLoss: number;
  largestWin: number;
  largestLoss: number;
  consecutiveWins: number;
  consecutiveLosses: number;
  averageHoldingPeriod: number;
  tradingFrequency: number;
  informationRatio: number;
  treynorRatio: number;
  jensenAlpha: number;
}

export interface RiskAdjustedMetrics {
  beta: number;
  alpha: number;
  trackingError: number;
  informationRatio: number;
  upCaptureRatio: number;
  downCaptureRatio: number;
  battingAverage: number;
  gainLossRatio: number;
}

export interface DrawdownAnalysis {
  maxDrawdown: number;
  maxDrawdownDuration: number;
  averageDrawdown: number;
  averageDrawdownDuration: number;
  drawdownPeriods: {
    start: Date;
    end: Date;
    duration: number;
    magnitude: number;
    recovery: Date | null;
  }[];
}

export interface MonthlyAnalysis {
  monthlyReturns: { month: string; return: number; benchmark?: number }[];
  bestMonth: { month: string; return: number };
  worstMonth: { month: string; return: number };
  positiveMonths: number;
  negativeMonths: number;
  averageMonthlyReturn: number;
  monthlyVolatility: number;
}

export class PerformanceAnalyticsService {
  private riskFreeRate: number;
  private benchmarkReturns?: number[];

  constructor(riskFreeRate: number = 0.02, benchmarkReturns?: number[]) {
    this.riskFreeRate = riskFreeRate;
    this.benchmarkReturns = benchmarkReturns;
  }

  /**
   * Calculate comprehensive performance metrics
   */
  public calculatePerformanceMetrics(
    trades: BacktestTrade[],
    equityCurve: { date: Date; equity: number }[],
    initialCapital: number,
    tradingPeriodDays: number
  ): PerformanceMetrics {
    try {
      if (trades.length === 0 || equityCurve.length === 0) {
        throw new Error('Insufficient data for performance analysis');
      }

      const finalEquity = equityCurve[equityCurve.length - 1].equity;
      const totalReturn = (finalEquity - initialCapital) / initialCapital;
      const annualizedReturn = Math.pow(1 + totalReturn, 365 / tradingPeriodDays) - 1;

      // Calculate returns series
      const returns = this.calculateReturns(equityCurve);
      const volatility = this.calculateVolatility(returns) * Math.sqrt(252); // Annualized

      // Risk-adjusted metrics
      const sharpeRatio = this.calculateSharpeRatio(returns, volatility);
      const sortinoRatio = this.calculateSortinoRatio(returns);
      const calmarRatio = this.calculateCalmarRatio(annualizedReturn, equityCurve, initialCapital);

      // Drawdown analysis
      const maxDrawdown = this.calculateMaxDrawdown(equityCurve, initialCapital);

      // Trade-based metrics
      const winningTrades = trades.filter(t => t.pnl > 0);
      const losingTrades = trades.filter(t => t.pnl < 0);
      
      const winRate = trades.length > 0 ? winningTrades.length / trades.length : 0;
      const averageWin = winningTrades.length > 0 ? 
        winningTrades.reduce((sum, t) => sum + t.pnl, 0) / winningTrades.length : 0;
      const averageLoss = losingTrades.length > 0 ? 
        Math.abs(losingTrades.reduce((sum, t) => sum + t.pnl, 0) / losingTrades.length) : 0;
      
      const profitFactor = averageLoss > 0 ? (averageWin * winningTrades.length) / (averageLoss * losingTrades.length) : 0;
      
      const largestWin = winningTrades.length > 0 ? Math.max(...winningTrades.map(t => t.pnl)) : 0;
      const largestLoss = losingTrades.length > 0 ? Math.min(...losingTrades.map(t => t.pnl)) : 0;

      // Consecutive wins/losses
      const { consecutiveWins, consecutiveLosses } = this.calculateConsecutiveWinsLosses(trades);

      // Other metrics
      const averageHoldingPeriod = trades.length > 0 ? 
        trades.reduce((sum, t) => sum + t.holdingPeriod, 0) / trades.length : 0;
      const tradingFrequency = trades.length / (tradingPeriodDays / 365); // Trades per year

      // Advanced metrics (require benchmark)
      const informationRatio = this.calculateInformationRatio(returns);
      const treynorRatio = this.calculateTreynorRatio(annualizedReturn);
      const jensenAlpha = this.calculateJensenAlpha(returns);

      logger.info('Performance metrics calculated', {
        totalReturn: totalReturn * 100,
        sharpeRatio,
        winRate: winRate * 100,
        maxDrawdown: maxDrawdown * 100
      });

      return {
        totalReturn,
        annualizedReturn,
        volatility,
        sharpeRatio,
        sortinoRatio,
        calmarRatio,
        maxDrawdown,
        winRate,
        profitFactor,
        averageWin,
        averageLoss,
        largestWin,
        largestLoss,
        consecutiveWins,
        consecutiveLosses,
        averageHoldingPeriod,
        tradingFrequency,
        informationRatio,
        treynorRatio,
        jensenAlpha
      };
    } catch (error) {
      logger.error('Error calculating performance metrics:', error);
      throw error;
    }
  }

  /**
   * Calculate detailed drawdown analysis
   */
  public analyzeDrawdowns(
    equityCurve: { date: Date; equity: number }[],
    initialCapital: number
  ): DrawdownAnalysis {
    const drawdownPeriods: DrawdownAnalysis['drawdownPeriods'] = [];
    let peak = initialCapital;
    let peakDate = equityCurve[0]?.date;
    let inDrawdown = false;
    let drawdownStart: Date | null = null;
    let maxDrawdownInPeriod = 0;

    for (let i = 0; i < equityCurve.length; i++) {
      const point = equityCurve[i];
      
      if (point.equity > peak) {
        // New peak - end any current drawdown
        if (inDrawdown && drawdownStart) {
          drawdownPeriods.push({
            start: drawdownStart,
            end: equityCurve[i - 1].date,
            duration: this.daysBetween(drawdownStart, equityCurve[i - 1].date),
            magnitude: maxDrawdownInPeriod,
            recovery: point.date
          });
          inDrawdown = false;
          maxDrawdownInPeriod = 0;
        }
        peak = point.equity;
        peakDate = point.date;
      } else if (point.equity < peak) {
        // In drawdown
        if (!inDrawdown) {
          inDrawdown = true;
          drawdownStart = point.date;
        }
        const currentDrawdown = (peak - point.equity) / peak;
        maxDrawdownInPeriod = Math.max(maxDrawdownInPeriod, currentDrawdown);
      }
    }

    // Handle ongoing drawdown
    if (inDrawdown && drawdownStart) {
      drawdownPeriods.push({
        start: drawdownStart,
        end: equityCurve[equityCurve.length - 1].date,
        duration: this.daysBetween(drawdownStart, equityCurve[equityCurve.length - 1].date),
        magnitude: maxDrawdownInPeriod,
        recovery: null
      });
    }

    const maxDrawdown = Math.max(...drawdownPeriods.map(d => d.magnitude), 0);
    const maxDrawdownDuration = Math.max(...drawdownPeriods.map(d => d.duration), 0);
    const averageDrawdown = drawdownPeriods.length > 0 ? 
      drawdownPeriods.reduce((sum, d) => sum + d.magnitude, 0) / drawdownPeriods.length : 0;
    const averageDrawdownDuration = drawdownPeriods.length > 0 ? 
      drawdownPeriods.reduce((sum, d) => sum + d.duration, 0) / drawdownPeriods.length : 0;

    return {
      maxDrawdown,
      maxDrawdownDuration,
      averageDrawdown,
      averageDrawdownDuration,
      drawdownPeriods
    };
  }

  /**
   * Analyze monthly performance
   */
  public analyzeMonthlyPerformance(
    equityCurve: { date: Date; equity: number }[]
  ): MonthlyAnalysis {
    const monthlyData = new Map<string, { start: number; end: number }>();
    
    equityCurve.forEach(point => {
      const monthKey = `${point.date.getFullYear()}-${String(point.date.getMonth() + 1).padStart(2, '0')}`;
      
      if (!monthlyData.has(monthKey)) {
        monthlyData.set(monthKey, { start: point.equity, end: point.equity });
      } else {
        monthlyData.get(monthKey)!.end = point.equity;
      }
    });

    const monthlyReturns = Array.from(monthlyData.entries()).map(([month, data]) => ({
      month,
      return: (data.end - data.start) / data.start
    }));

    const returns = monthlyReturns.map(m => m.return);
    const bestMonth = monthlyReturns.reduce((best, current) => 
      current.return > best.return ? current : best, monthlyReturns[0]);
    const worstMonth = monthlyReturns.reduce((worst, current) => 
      current.return < worst.return ? current : worst, monthlyReturns[0]);

    const positiveMonths = returns.filter(r => r > 0).length;
    const negativeMonths = returns.filter(r => r < 0).length;
    const averageMonthlyReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const monthlyVolatility = this.calculateVolatility(returns);

    return {
      monthlyReturns,
      bestMonth,
      worstMonth,
      positiveMonths,
      negativeMonths,
      averageMonthlyReturn,
      monthlyVolatility
    };
  }

  /**
   * Calculate risk-adjusted metrics relative to benchmark
   */
  public calculateRiskAdjustedMetrics(
    returns: number[],
    benchmarkReturns?: number[]
  ): RiskAdjustedMetrics {
    const beta = this.calculateBeta(returns, benchmarkReturns);
    const alpha = this.calculateAlpha(returns, benchmarkReturns, beta);
    const trackingError = this.calculateTrackingError(returns, benchmarkReturns);
    const informationRatio = this.calculateInformationRatio(returns, benchmarkReturns);
    
    const { upCaptureRatio, downCaptureRatio } = this.calculateCaptureRatios(returns, benchmarkReturns);
    const battingAverage = this.calculateBattingAverage(returns, benchmarkReturns);
    const gainLossRatio = this.calculateGainLossRatio(returns);

    return {
      beta,
      alpha,
      trackingError,
      informationRatio,
      upCaptureRatio,
      downCaptureRatio,
      battingAverage,
      gainLossRatio
    };
  }

  // Helper methods
  private calculateReturns(equityCurve: { date: Date; equity: number }[]): number[] {
    const returns = [];
    for (let i = 1; i < equityCurve.length; i++) {
      const dailyReturn = (equityCurve[i].equity - equityCurve[i - 1].equity) / equityCurve[i - 1].equity;
      returns.push(dailyReturn);
    }
    return returns;
  }

  private calculateVolatility(returns: number[]): number {
    if (returns.length === 0) return 0;
    const mean = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const variance = returns.reduce((sum, r) => sum + Math.pow(r - mean, 2), 0) / returns.length;
    return Math.sqrt(variance);
  }

  private calculateSharpeRatio(returns: number[], volatility: number): number {
    if (volatility === 0) return 0;
    const avgReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const excessReturn = avgReturn * 252 - this.riskFreeRate; // Annualized
    return excessReturn / volatility;
  }

  private calculateSortinoRatio(returns: number[]): number {
    const avgReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const negativeReturns = returns.filter(r => r < 0);
    
    if (negativeReturns.length === 0) return Infinity;
    
    const downwardDeviation = Math.sqrt(
      negativeReturns.reduce((sum, r) => sum + Math.pow(r, 2), 0) / negativeReturns.length
    );
    
    const annualizedReturn = avgReturn * 252;
    const annualizedDownwardDev = downwardDeviation * Math.sqrt(252);
    
    return annualizedDownwardDev > 0 ? (annualizedReturn - this.riskFreeRate) / annualizedDownwardDev : 0;
  }

  private calculateCalmarRatio(annualizedReturn: number, equityCurve: { date: Date; equity: number }[], initialCapital: number): number {
    const maxDrawdown = this.calculateMaxDrawdown(equityCurve, initialCapital);
    return maxDrawdown > 0 ? annualizedReturn / maxDrawdown : 0;
  }

  private calculateMaxDrawdown(equityCurve: { date: Date; equity: number }[], initialCapital: number): number {
    let maxDrawdown = 0;
    let peak = initialCapital;

    for (const point of equityCurve) {
      if (point.equity > peak) {
        peak = point.equity;
      } else {
        const drawdown = (peak - point.equity) / peak;
        maxDrawdown = Math.max(maxDrawdown, drawdown);
      }
    }

    return maxDrawdown;
  }

  private calculateConsecutiveWinsLosses(trades: BacktestTrade[]): { consecutiveWins: number; consecutiveLosses: number } {
    let maxConsecutiveWins = 0;
    let maxConsecutiveLosses = 0;
    let currentWins = 0;
    let currentLosses = 0;

    for (const trade of trades) {
      if (trade.pnl > 0) {
        currentWins++;
        currentLosses = 0;
        maxConsecutiveWins = Math.max(maxConsecutiveWins, currentWins);
      } else {
        currentLosses++;
        currentWins = 0;
        maxConsecutiveLosses = Math.max(maxConsecutiveLosses, currentLosses);
      }
    }

    return { consecutiveWins: maxConsecutiveWins, consecutiveLosses: maxConsecutiveLosses };
  }

  private calculateBeta(returns: number[], benchmarkReturns?: number[]): number {
    if (!benchmarkReturns || benchmarkReturns.length !== returns.length) return 1;
    
    const covariance = this.calculateCovariance(returns, benchmarkReturns);
    const benchmarkVariance = this.calculateVolatility(benchmarkReturns) ** 2;
    
    return benchmarkVariance > 0 ? covariance / benchmarkVariance : 1;
  }

  private calculateAlpha(returns: number[], benchmarkReturns?: number[], beta?: number): number {
    if (!benchmarkReturns || !beta) return 0;
    
    const avgReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const avgBenchmarkReturn = benchmarkReturns.reduce((sum, r) => sum + r, 0) / benchmarkReturns.length;
    
    return (avgReturn * 252) - (this.riskFreeRate + beta * (avgBenchmarkReturn * 252 - this.riskFreeRate));
  }

  private calculateCovariance(returns1: number[], returns2: number[]): number {
    if (returns1.length !== returns2.length) return 0;
    
    const mean1 = returns1.reduce((sum, r) => sum + r, 0) / returns1.length;
    const mean2 = returns2.reduce((sum, r) => sum + r, 0) / returns2.length;
    
    const covariance = returns1.reduce((sum, r1, i) => {
      return sum + (r1 - mean1) * (returns2[i] - mean2);
    }, 0) / returns1.length;
    
    return covariance;
  }

  private calculateTrackingError(returns: number[], benchmarkReturns?: number[]): number {
    if (!benchmarkReturns || benchmarkReturns.length !== returns.length) return 0;
    
    const excessReturns = returns.map((r, i) => r - benchmarkReturns[i]);
    return this.calculateVolatility(excessReturns) * Math.sqrt(252);
  }

  private calculateInformationRatio(returns: number[], benchmarkReturns?: number[]): number {
    if (!benchmarkReturns) return 0;
    
    const trackingError = this.calculateTrackingError(returns, benchmarkReturns);
    if (trackingError === 0) return 0;
    
    const avgReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const avgBenchmarkReturn = benchmarkReturns.reduce((sum, r) => sum + r, 0) / benchmarkReturns.length;
    const excessReturn = (avgReturn - avgBenchmarkReturn) * 252;
    
    return excessReturn / trackingError;
  }

  private calculateTreynorRatio(annualizedReturn: number): number {
    // Simplified - would need actual beta calculation
    const beta = 1; // Assuming beta of 1 for simplification
    return beta > 0 ? (annualizedReturn - this.riskFreeRate) / beta : 0;
  }

  private calculateJensenAlpha(returns: number[]): number {
    // Simplified Jensen's Alpha calculation
    const avgReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const beta = 1; // Simplified
    const marketReturn = 0.10; // Assumed market return
    
    return (avgReturn * 252) - (this.riskFreeRate + beta * (marketReturn - this.riskFreeRate));
  }

  private calculateCaptureRatios(returns: number[], benchmarkReturns?: number[]): { upCaptureRatio: number; downCaptureRatio: number } {
    if (!benchmarkReturns || benchmarkReturns.length !== returns.length) {
      return { upCaptureRatio: 1, downCaptureRatio: 1 };
    }
    
    const upPeriods = returns.filter((r, i) => benchmarkReturns[i] > 0);
    const downPeriods = returns.filter((r, i) => benchmarkReturns[i] < 0);
    const upBenchmarkPeriods = benchmarkReturns.filter(r => r > 0);
    const downBenchmarkPeriods = benchmarkReturns.filter(r => r < 0);
    
    const upCaptureRatio = upBenchmarkPeriods.length > 0 ? 
      (upPeriods.reduce((sum, r) => sum + r, 0) / upPeriods.length) / 
      (upBenchmarkPeriods.reduce((sum, r) => sum + r, 0) / upBenchmarkPeriods.length) : 1;
    
    const downCaptureRatio = downBenchmarkPeriods.length > 0 ? 
      (downPeriods.reduce((sum, r) => sum + r, 0) / downPeriods.length) / 
      (downBenchmarkPeriods.reduce((sum, r) => sum + r, 0) / downBenchmarkPeriods.length) : 1;
    
    return { upCaptureRatio, downCaptureRatio };
  }

  private calculateBattingAverage(returns: number[], benchmarkReturns?: number[]): number {
    if (!benchmarkReturns || benchmarkReturns.length !== returns.length) return 0.5;
    
    const outperformingPeriods = returns.filter((r, i) => r > benchmarkReturns[i]).length;
    return outperformingPeriods / returns.length;
  }

  private calculateGainLossRatio(returns: number[]): number {
    const gains = returns.filter(r => r > 0);
    const losses = returns.filter(r => r < 0);
    
    if (losses.length === 0) return Infinity;
    
    const avgGain = gains.reduce((sum, r) => sum + r, 0) / gains.length;
    const avgLoss = Math.abs(losses.reduce((sum, r) => sum + r, 0) / losses.length);
    
    return avgLoss > 0 ? avgGain / avgLoss : 0;
  }

  private daysBetween(date1: Date, date2: Date): number {
    const diffTime = Math.abs(date2.getTime() - date1.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }
}
