import { Schema, model, Document } from 'mongoose';

export interface IDiscoveredTicker extends Document {
  ticker: string;
  companyName: string;
  sector: string;
  marketCap: number;
  discoveryReason: string;
  impactScore: number;
  confidence: number;
  newsArticles: Array<{
    title: string;
    content: string;
    source: string;
    publishedAt: Date;
    url: string;
    category: string;
    country: string;
    language: string;
    relevanceScore: number;
  }>;
  tradingOpportunities: number;
  riskLevel: 'low' | 'medium' | 'high';
  timeframe: string;
  discoveredAt: Date;
}

const NewsArticleSchema = new Schema({
  title: { type: String, required: true },
  content: { type: String, required: true },
  source: { type: String, required: true },
  publishedAt: { type: Date, required: true },
  url: { type: String, required: true },
  category: { type: String, required: true },
  country: { type: String, required: true },
  language: { type: String, required: true },
  relevanceScore: { type: Number, required: true }
});

const DiscoveredTickerSchema = new Schema<IDiscoveredTicker>({
  ticker: { type: String, required: true, index: true },
  companyName: { type: String, required: true },
  sector: { type: String, required: true },
  marketCap: { type: Number, required: true },
  discoveryReason: { type: String, required: true },
  impactScore: { type: Number, required: true },
  confidence: { type: Number, required: true },
  newsArticles: [NewsArticleSchema],
  tradingOpportunities: { type: Number, required: true },
  riskLevel: { type: String, enum: ['low', 'medium', 'high'], required: true },
  timeframe: { type: String, required: true },
  discoveredAt: { type: Date, required: true, default: Date.now }
}, {
  timestamps: true
});

// Create indexes for better query performance
DiscoveredTickerSchema.index({ discoveredAt: -1 });
DiscoveredTickerSchema.index({ sector: 1 });
DiscoveredTickerSchema.index({ riskLevel: 1 });
DiscoveredTickerSchema.index({ confidence: -1 });

export const DiscoveredTickerModel = model<IDiscoveredTicker>('DiscoveredTicker', DiscoveredTickerSchema);
