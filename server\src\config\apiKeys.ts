import { logger } from '../utils/logger';

export interface ApiKeysConfig {
  newsApi: {
    key: string;
    baseUrl: string;
    enabled: boolean;
  };
  googleAi: {
    key: string;
    enabled: boolean;
  };
  alphaVantage: {
    key: string;
    baseUrl: string;
    enabled: boolean;
  };
  yahooFinance: {
    enabled: boolean;
  };
  polygon: {
    key: string;
    baseUrl: string;
    enabled: boolean;
  };
}

class ApiKeysManager {
  private config: ApiKeysConfig;

  constructor() {
    this.config = this.loadConfiguration();
    this.validateConfiguration();
  }

  private loadConfiguration(): ApiKeysConfig {
    return {
      newsApi: {
        key: process.env.NEWS_API_KEY || '',
        baseUrl: 'https://newsapi.org/v2',
        enabled: !!process.env.NEWS_API_KEY
      },
      googleAi: {
        key: process.env.GOOGLE_AI_API_KEY || '',
        enabled: !!process.env.GOOGLE_AI_API_KEY
      },
      alphaVantage: {
        key: process.env.ALPHA_VANTAGE_API_KEY || '',
        baseUrl: 'https://www.alphavantage.co/query',
        enabled: !!process.env.ALPHA_VANTAGE_API_KEY
      },
      yahooFinance: {
        enabled: true // Yahoo Finance doesn't require API key for basic usage
      },
      polygon: {
        key: process.env.POLYGON_API_KEY || '',
        baseUrl: 'https://api.polygon.io',
        enabled: !!process.env.POLYGON_API_KEY
      }
    };
  }

  private validateConfiguration(): void {
    const warnings: string[] = [];
    const errors: string[] = [];

    // Check for missing API keys
    if (!this.config.newsApi.enabled) {
      warnings.push('NEWS_API_KEY not configured - news sentiment analysis will use mock data');
    }

    if (!this.config.googleAi.enabled) {
      warnings.push('GOOGLE_AI_API_KEY not configured - AI analysis will use fallback methods');
    }

    if (!this.config.alphaVantage.enabled) {
      warnings.push('ALPHA_VANTAGE_API_KEY not configured - will rely on Yahoo Finance for market data');
    }

    if (!this.config.polygon.enabled) {
      warnings.push('POLYGON_API_KEY not configured - high-frequency data features disabled');
    }

    // Log warnings and errors
    warnings.forEach(warning => logger.warn(`API Configuration: ${warning}`));
    errors.forEach(error => logger.error(`API Configuration: ${error}`));

    if (errors.length > 0) {
      throw new Error(`Critical API configuration errors: ${errors.join(', ')}`);
    }

    // Log successful configuration
    const enabledServices = Object.entries(this.config)
      .filter(([, config]) => config.enabled)
      .map(([service]) => service);

    logger.info(`API Services configured: ${enabledServices.join(', ')}`);
  }

  public getConfig(): ApiKeysConfig {
    return { ...this.config };
  }

  public isServiceEnabled(service: keyof ApiKeysConfig): boolean {
    return this.config[service].enabled;
  }

  public getServiceConfig<T extends keyof ApiKeysConfig>(service: T): ApiKeysConfig[T] {
    return this.config[service];
  }

  public updateApiKey(service: keyof ApiKeysConfig, key: string): void {
    if ('key' in this.config[service]) {
      (this.config[service] as any).key = key;
      (this.config[service] as any).enabled = !!key;
      logger.info(`Updated API key for ${service}`);
    }
  }

  public getHealthStatus(): Record<string, boolean> {
    return Object.fromEntries(
      Object.entries(this.config).map(([service, config]) => [
        service,
        config.enabled
      ])
    );
  }
}

// Singleton instance
export const apiKeysManager = new ApiKeysManager();

// Helper functions for backward compatibility
export const getApiKeys = () => apiKeysManager.getConfig();
export const isServiceEnabled = (service: keyof ApiKeysConfig) => apiKeysManager.isServiceEnabled(service);
export const getServiceConfig = <T extends keyof ApiKeysConfig>(service: T) => apiKeysManager.getServiceConfig(service);

// Environment setup instructions
export const getSetupInstructions = (): string => {
  return `
API Keys Setup Instructions:

1. Create a .env file in the server directory with the following variables:

# News API (for news sentiment analysis)
NEWS_API_KEY=your_newsapi_key_here
# Get your key at: https://newsapi.org/

# Google AI API (for advanced AI analysis)
GOOGLE_AI_API_KEY=your_google_ai_key_here
# Get your key at: https://makersuite.google.com/app/apikey

# Alpha Vantage (for market data)
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key_here
# Get your key at: https://www.alphavantage.co/support/#api-key

# Polygon.io (for high-frequency data)
POLYGON_API_KEY=your_polygon_key_here
# Get your key at: https://polygon.io/

2. Restart the server after adding the API keys.

3. Check the server logs to confirm which services are enabled.

Note: The system will work with mock data if API keys are not provided,
but real-time features will be limited.
  `.trim();
};
