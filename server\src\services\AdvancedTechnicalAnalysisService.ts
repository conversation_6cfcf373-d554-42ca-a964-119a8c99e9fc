import { RSI, MACD, BollingerBands, SMA, EMA, ATR, Stochastic, WilliamsR, OBV } from 'technicalindicators';
import { OHLCV, TechnicalIndicators, SignalType } from '../types';
import { SETTINGS } from '../config/settings';
import { logger } from '../utils/logger';

export interface AdvancedTechnicalIndicators extends TechnicalIndicators {
  fibonacci: {
    retracements: { level: number; price: number }[];
    extensions: { level: number; price: number }[];
  };
  supportResistance: {
    support: number[];
    resistance: number[];
    pivotPoint: number;
  };
  volumeAnalysis: {
    obv: number;
    volumeProfile: { price: number; volume: number }[];
    volumeTrend: 'increasing' | 'decreasing' | 'neutral';
  };
  patternRecognition: {
    patterns: string[];
    trendDirection: 'uptrend' | 'downtrend' | 'sideways';
    trendStrength: number; // 0-100
  };
  multiTimeframe: {
    '1h': Partial<TechnicalIndicators>;
    '4h': Partial<TechnicalIndicators>;
    '1d': Partial<TechnicalIndicators>;
  };
  ichimoku: {
    tenkanSen: number;
    kijunSen: number;
    senkouSpanA: number;
    senkouSpanB: number;
    chikouSpan: number;
  };
  stochastic: {
    k: number;
    d: number;
    signal: SignalType;
  };
  williamsR: {
    value: number;
    signal: SignalType;
  };
}

export interface SignalStrength {
  overall: number; // 0-100
  bullish: number;
  bearish: number;
  signals: {
    indicator: string;
    signal: SignalType;
    strength: number;
    weight: number;
  }[];
}

export class AdvancedTechnicalAnalysisService {
  /**
   * Calculate Fibonacci retracements and extensions
   */
  private calculateFibonacci(ohlcv: OHLCV[]): {
    retracements: { level: number; price: number }[];
    extensions: { level: number; price: number }[];
  } {
    try {
      const prices = ohlcv.map(candle => candle.high);
      const lows = ohlcv.map(candle => candle.low);
      
      // Find swing high and low for Fibonacci calculation
      const recentHigh = Math.max(...prices.slice(-50));
      const recentLow = Math.min(...lows.slice(-50));
      const range = recentHigh - recentLow;

      const fibLevels = [0.236, 0.382, 0.5, 0.618, 0.786];
      const extLevels = [1.272, 1.414, 1.618, 2.618];

      const retracements = fibLevels.map(level => ({
        level,
        price: recentHigh - (range * level)
      }));

      const extensions = extLevels.map(level => ({
        level,
        price: recentHigh + (range * (level - 1))
      }));

      return { retracements, extensions };
    } catch (error) {
      logger.error('Error calculating Fibonacci levels:', error);
      return { retracements: [], extensions: [] };
    }
  }

  /**
   * Calculate support and resistance levels
   */
  private calculateSupportResistance(ohlcv: OHLCV[]): {
    support: number[];
    resistance: number[];
    pivotPoint: number;
  } {
    try {
      const highs = ohlcv.map(candle => candle.high);
      const lows = ohlcv.map(candle => candle.low);
      const closes = ohlcv.map(candle => candle.close);

      // Calculate pivot point (yesterday's data)
      const lastCandle = ohlcv[ohlcv.length - 1];
      const pivotPoint = (lastCandle.high + lastCandle.low + lastCandle.close) / 3;

      // Find support and resistance levels using local extremes
      const support: number[] = [];
      const resistance: number[] = [];

      // Look for local minima (support) and maxima (resistance)
      for (let i = 2; i < ohlcv.length - 2; i++) {
        const current = ohlcv[i];
        const prev2 = ohlcv[i - 2];
        const prev1 = ohlcv[i - 1];
        const next1 = ohlcv[i + 1];
        const next2 = ohlcv[i + 2];

        // Local minimum (support)
        if (current.low < prev2.low && current.low < prev1.low && 
            current.low < next1.low && current.low < next2.low) {
          support.push(current.low);
        }

        // Local maximum (resistance)
        if (current.high > prev2.high && current.high > prev1.high && 
            current.high > next1.high && current.high > next2.high) {
          resistance.push(current.high);
        }
      }

      // Keep only the most significant levels (last 5 of each)
      return {
        support: support.slice(-5),
        resistance: resistance.slice(-5),
        pivotPoint
      };
    } catch (error) {
      logger.error('Error calculating support/resistance:', error);
      return { support: [], resistance: [], pivotPoint: 0 };
    }
  }

  /**
   * Calculate volume analysis indicators
   */
  private calculateVolumeAnalysis(ohlcv: OHLCV[]): {
    obv: number;
    volumeProfile: { price: number; volume: number }[];
    volumeTrend: 'increasing' | 'decreasing' | 'neutral';
  } {
    try {
      const closes = ohlcv.map(candle => candle.close);
      const volumes = ohlcv.map(candle => candle.volume);

      // Calculate OBV
      const obvValues = OBV.calculate({ close: closes, volume: volumes });
      const obv = obvValues[obvValues.length - 1] || 0;

      // Simple volume profile (price levels with volume)
      const volumeProfile = ohlcv.slice(-20).map(candle => ({
        price: (candle.high + candle.low) / 2,
        volume: candle.volume
      }));

      // Volume trend analysis
      const recentVolumes = volumes.slice(-10);
      const firstHalf = recentVolumes.slice(0, 5).reduce((a, b) => a + b, 0) / 5;
      const secondHalf = recentVolumes.slice(5).reduce((a, b) => a + b, 0) / 5;
      
      let volumeTrend: 'increasing' | 'decreasing' | 'neutral' = 'neutral';
      if (secondHalf > firstHalf * 1.1) {
        volumeTrend = 'increasing';
      } else if (secondHalf < firstHalf * 0.9) {
        volumeTrend = 'decreasing';
      }

      return { obv, volumeProfile, volumeTrend };
    } catch (error) {
      logger.error('Error calculating volume analysis:', error);
      return { obv: 0, volumeProfile: [], volumeTrend: 'neutral' };
    }
  }

  /**
   * Pattern recognition and trend analysis
   */
  private analyzePatterns(ohlcv: OHLCV[]): {
    patterns: string[];
    trendDirection: 'uptrend' | 'downtrend' | 'sideways';
    trendStrength: number;
  } {
    try {
      const patterns: string[] = [];
      const closes = ohlcv.map(candle => candle.close);
      const highs = ohlcv.map(candle => candle.high);
      const lows = ohlcv.map(candle => candle.low);

      // Simple trend analysis using moving averages
      const sma20 = SMA.calculate({ values: closes, period: 20 });
      const sma50 = SMA.calculate({ values: closes, period: 50 });
      
      const currentPrice = closes[closes.length - 1];
      const currentSMA20 = sma20[sma20.length - 1];
      const currentSMA50 = sma50[sma50.length - 1];

      // Determine trend direction
      let trendDirection: 'uptrend' | 'downtrend' | 'sideways' = 'sideways';
      if (currentPrice > currentSMA20 && currentSMA20 > currentSMA50) {
        trendDirection = 'uptrend';
      } else if (currentPrice < currentSMA20 && currentSMA20 < currentSMA50) {
        trendDirection = 'downtrend';
      }

      // Calculate trend strength (0-100)
      const priceToSMA20Ratio = currentPrice / currentSMA20;
      const sma20ToSMA50Ratio = currentSMA20 / currentSMA50;
      const trendStrength = Math.min(100, Math.abs((priceToSMA20Ratio - 1) * 100 + (sma20ToSMA50Ratio - 1) * 100));

      // Simple pattern detection
      const recent = ohlcv.slice(-10);
      
      // Doji pattern detection
      const lastCandle = recent[recent.length - 1];
      const bodySize = Math.abs(lastCandle.close - lastCandle.open);
      const totalRange = lastCandle.high - lastCandle.low;
      if (bodySize < totalRange * 0.1) {
        patterns.push('Doji');
      }

      // Hammer/Hanging Man
      const lowerShadow = Math.min(lastCandle.open, lastCandle.close) - lastCandle.low;
      const upperShadow = lastCandle.high - Math.max(lastCandle.open, lastCandle.close);
      if (lowerShadow > bodySize * 2 && upperShadow < bodySize * 0.5) {
        patterns.push(trendDirection === 'downtrend' ? 'Hammer' : 'Hanging Man');
      }

      return { patterns, trendDirection, trendStrength };
    } catch (error) {
      logger.error('Error in pattern analysis:', error);
      return { patterns: [], trendDirection: 'sideways', trendStrength: 0 };
    }
  }

  /**
   * Calculate Ichimoku Cloud indicators
   */
  private calculateIchimoku(ohlcv: OHLCV[]): {
    tenkanSen: number;
    kijunSen: number;
    senkouSpanA: number;
    senkouSpanB: number;
    chikouSpan: number;
  } {
    try {
      const highs = ohlcv.map(candle => candle.high);
      const lows = ohlcv.map(candle => candle.low);
      const closes = ohlcv.map(candle => candle.close);

      // Tenkan-sen (Conversion Line): (9-period high + 9-period low) / 2
      const tenkanHigh = Math.max(...highs.slice(-9));
      const tenkanLow = Math.min(...lows.slice(-9));
      const tenkanSen = (tenkanHigh + tenkanLow) / 2;

      // Kijun-sen (Base Line): (26-period high + 26-period low) / 2
      const kijunHigh = Math.max(...highs.slice(-26));
      const kijunLow = Math.min(...lows.slice(-26));
      const kijunSen = (kijunHigh + kijunLow) / 2;

      // Senkou Span A (Leading Span A): (Tenkan-sen + Kijun-sen) / 2
      const senkouSpanA = (tenkanSen + kijunSen) / 2;

      // Senkou Span B (Leading Span B): (52-period high + 52-period low) / 2
      const senkouHigh = Math.max(...highs.slice(-52));
      const senkouLow = Math.min(...lows.slice(-52));
      const senkouSpanB = (senkouHigh + senkouLow) / 2;

      // Chikou Span (Lagging Span): Current closing price plotted 26 periods back
      const chikouSpan = closes[closes.length - 1];

      return { tenkanSen, kijunSen, senkouSpanA, senkouSpanB, chikouSpan };
    } catch (error) {
      logger.error('Error calculating Ichimoku:', error);
      return { tenkanSen: 0, kijunSen: 0, senkouSpanA: 0, senkouSpanB: 0, chikouSpan: 0 };
    }
  }

  /**
   * Calculate Stochastic oscillator
   */
  private calculateStochastic(ohlcv: OHLCV[]): { k: number; d: number; signal: SignalType } {
    try {
      const highs = ohlcv.map(candle => candle.high);
      const lows = ohlcv.map(candle => candle.low);
      const closes = ohlcv.map(candle => candle.close);

      const stochValues = Stochastic.calculate({
        high: highs,
        low: lows,
        close: closes,
        period: 14,
        signalPeriod: 3
      });

      const lastStoch = stochValues[stochValues.length - 1];
      const k = lastStoch?.k || 50;
      const d = lastStoch?.d || 50;

      let signal: SignalType = 'neutral';
      if (k < 20 && d < 20) {
        signal = 'bullish'; // Oversold
      } else if (k > 80 && d > 80) {
        signal = 'bearish'; // Overbought
      }

      return { k, d, signal };
    } catch (error) {
      logger.error('Error calculating Stochastic:', error);
      return { k: 50, d: 50, signal: 'neutral' };
    }
  }

  /**
   * Calculate Williams %R
   */
  private calculateWilliamsR(ohlcv: OHLCV[]): { value: number; signal: SignalType } {
    try {
      const highs = ohlcv.map(candle => candle.high);
      const lows = ohlcv.map(candle => candle.low);
      const closes = ohlcv.map(candle => candle.close);

      const williamsRValues = WilliamsR.calculate({
        high: highs,
        low: lows,
        close: closes,
        period: 14
      });

      const value = williamsRValues[williamsRValues.length - 1] || -50;

      let signal: SignalType = 'neutral';
      if (value < -80) {
        signal = 'bullish'; // Oversold
      } else if (value > -20) {
        signal = 'bearish'; // Overbought
      }

      return { value, signal };
    } catch (error) {
      logger.error('Error calculating Williams %R:', error);
      return { value: -50, signal: 'neutral' };
    }
  }

  /**
   * Calculate all advanced technical indicators
   */
  public calculateAdvancedIndicators(ohlcv: OHLCV[]): AdvancedTechnicalIndicators {
    if (ohlcv.length < 100) {
      throw new Error('Insufficient data for advanced technical analysis. Need at least 100 data points.');
    }

    const prices = ohlcv.map(candle => candle.close);
    
    // Calculate basic indicators first
    const basicIndicators: TechnicalIndicators = {
      rsi: this.calculateRSI(prices),
      macd: this.calculateMACD(prices),
      bollingerBands: this.calculateBollingerBands(prices),
      sma: this.calculateSMA(prices),
      atr: this.calculateATR(ohlcv),
      adx: this.calculateADX(ohlcv)
    };

    // Calculate advanced indicators
    const fibonacci = this.calculateFibonacci(ohlcv);
    const supportResistance = this.calculateSupportResistance(ohlcv);
    const volumeAnalysis = this.calculateVolumeAnalysis(ohlcv);
    const patternRecognition = this.analyzePatterns(ohlcv);
    const ichimoku = this.calculateIchimoku(ohlcv);
    const stochastic = this.calculateStochastic(ohlcv);
    const williamsR = this.calculateWilliamsR(ohlcv);

    // Multi-timeframe analysis (simplified - would need actual multi-timeframe data)
    const multiTimeframe = {
      '1h': { rsi: basicIndicators.rsi },
      '4h': { rsi: basicIndicators.rsi },
      '1d': { rsi: basicIndicators.rsi }
    };

    const advancedIndicators: AdvancedTechnicalIndicators = {
      ...basicIndicators,
      fibonacci,
      supportResistance,
      volumeAnalysis,
      patternRecognition,
      multiTimeframe,
      ichimoku,
      stochastic,
      williamsR
    };

    logger.info('Advanced technical indicators calculated successfully', { 
      patterns: patternRecognition.patterns,
      trend: patternRecognition.trendDirection,
      strength: patternRecognition.trendStrength
    });

    return advancedIndicators;
  }

  // Helper methods from original TechnicalAnalysisService
  private calculateRSI(prices: number[]): number {
    try {
      const rsiValues = RSI.calculate({
        values: prices,
        period: SETTINGS.technicalIndicators.rsi.period
      });
      return rsiValues[rsiValues.length - 1] || 50;
    } catch (error) {
      logger.error('Error calculating RSI:', error);
      return 50;
    }
  }

  private calculateMACD(prices: number[]): { macd: number; signal: number; histogram: number } {
    try {
      const macdValues = MACD.calculate({
        values: prices,
        fastPeriod: SETTINGS.technicalIndicators.macd.fastPeriod,
        slowPeriod: SETTINGS.technicalIndicators.macd.slowPeriod,
        signalPeriod: SETTINGS.technicalIndicators.macd.signalPeriod,
        SimpleMAOscillator: false,
        SimpleMASignal: false
      });
      
      const lastMACD = macdValues[macdValues.length - 1];
      return {
        macd: lastMACD?.MACD || 0,
        signal: lastMACD?.signal || 0,
        histogram: lastMACD?.histogram || 0
      };
    } catch (error) {
      logger.error('Error calculating MACD:', error);
      return { macd: 0, signal: 0, histogram: 0 };
    }
  }

  private calculateBollingerBands(prices: number[]): { upper: number; middle: number; lower: number } {
    try {
      const bbValues = BollingerBands.calculate({
        values: prices,
        period: SETTINGS.technicalIndicators.bollingerBands.period,
        stdDev: SETTINGS.technicalIndicators.bollingerBands.stdDev
      });
      
      const lastBB = bbValues[bbValues.length - 1];
      return {
        upper: lastBB?.upper || 0,
        middle: lastBB?.middle || 0,
        lower: lastBB?.lower || 0
      };
    } catch (error) {
      logger.error('Error calculating Bollinger Bands:', error);
      return { upper: 0, middle: 0, lower: 0 };
    }
  }

  private calculateSMA(prices: number[]): { short: number; long: number; crossover: SignalType } {
    try {
      const shortSMA = SMA.calculate({
        values: prices,
        period: SETTINGS.technicalIndicators.sma.shortPeriod
      });
      
      const longSMA = SMA.calculate({
        values: prices,
        period: SETTINGS.technicalIndicators.sma.longPeriod
      });
      
      const shortValue = shortSMA[shortSMA.length - 1] || 0;
      const longValue = longSMA[longSMA.length - 1] || 0;
      
      let crossover: SignalType = 'neutral';
      if (shortValue > longValue) {
        crossover = 'bullish';
      } else if (shortValue < longValue) {
        crossover = 'bearish';
      }
      
      return { short: shortValue, long: longValue, crossover };
    } catch (error) {
      logger.error('Error calculating SMA:', error);
      return { short: 0, long: 0, crossover: 'neutral' };
    }
  }

  private calculateATR(ohlcv: OHLCV[]): number {
    try {
      const high = ohlcv.map(candle => candle.high);
      const low = ohlcv.map(candle => candle.low);
      const close = ohlcv.map(candle => candle.close);

      const atrValues = ATR.calculate({
        high,
        low,
        close,
        period: SETTINGS.technicalIndicators.atr.period
      });

      return atrValues[atrValues.length - 1] || 0;
    } catch (error) {
      logger.error('Error calculating ATR:', error);
      return 0;
    }
  }

  /**
   * Calculate overall signal strength from all indicators
   */
  public calculateSignalStrength(indicators: AdvancedTechnicalIndicators): SignalStrength {
    const signals = [
      { indicator: 'RSI', signal: this.getRSISignal(indicators.rsi), strength: 70, weight: 0.15 },
      { indicator: 'MACD', signal: this.getMACDSignal(indicators.macd), strength: 80, weight: 0.20 },
      { indicator: 'Bollinger Bands', signal: this.getBollingerSignal(indicators.bollingerBands, indicators.rsi), strength: 60, weight: 0.10 },
      { indicator: 'SMA Crossover', signal: indicators.sma.crossover, strength: 75, weight: 0.15 },
      { indicator: 'Stochastic', signal: indicators.stochastic.signal, strength: 65, weight: 0.10 },
      { indicator: 'Williams %R', signal: indicators.williamsR.signal, strength: 60, weight: 0.10 },
      { indicator: 'Volume Trend', signal: this.getVolumeSignal(indicators.volumeAnalysis.volumeTrend), strength: 50, weight: 0.10 },
      { indicator: 'Pattern Recognition', signal: this.getPatternSignal(indicators.patternRecognition), strength: 40, weight: 0.10 }
    ];

    let bullishScore = 0;
    let bearishScore = 0;
    let totalWeight = 0;

    signals.forEach(signal => {
      if (signal.signal === 'bullish') {
        bullishScore += signal.strength * signal.weight;
      } else if (signal.signal === 'bearish') {
        bearishScore += signal.strength * signal.weight;
      }
      totalWeight += signal.weight;
    });

    const overall = Math.round(((bullishScore - bearishScore) / totalWeight + 100) / 2);

    return {
      overall: Math.max(0, Math.min(100, overall)),
      bullish: Math.round(bullishScore / totalWeight * 100),
      bearish: Math.round(bearishScore / totalWeight * 100),
      signals
    };
  }

  private getRSISignal(rsi: number): SignalType {
    if (rsi < SETTINGS.technicalIndicators.rsi.oversold) return 'bullish';
    if (rsi > SETTINGS.technicalIndicators.rsi.overbought) return 'bearish';
    return 'neutral';
  }

  private getMACDSignal(macd: { macd: number; signal: number; histogram: number }): SignalType {
    if (macd.histogram > 0 && macd.macd > macd.signal) return 'bullish';
    if (macd.histogram < 0 && macd.macd < macd.signal) return 'bearish';
    return 'neutral';
  }

  private getBollingerSignal(bb: { upper: number; middle: number; lower: number }, rsi: number): SignalType {
    // Simplified - would need current price for proper calculation
    if (rsi < 30) return 'bullish'; // Oversold near lower band
    if (rsi > 70) return 'bearish'; // Overbought near upper band
    return 'neutral';
  }

  private getVolumeSignal(volumeTrend: 'increasing' | 'decreasing' | 'neutral'): SignalType {
    if (volumeTrend === 'increasing') return 'bullish';
    if (volumeTrend === 'decreasing') return 'bearish';
    return 'neutral';
  }

  private getPatternSignal(pattern: { patterns: string[]; trendDirection: string; trendStrength: number }): SignalType {
    if (pattern.trendDirection === 'uptrend' && pattern.trendStrength > 50) return 'bullish';
    if (pattern.trendDirection === 'downtrend' && pattern.trendStrength > 50) return 'bearish';
    return 'neutral';
  }

  /**
   * Calculate Average Directional Index (ADX)
   */
  private calculateADX(ohlcv: OHLCV[]): number {
    if (ohlcv.length < 14) return 0;

    const period = 14;
    const trueRanges: number[] = [];
    const plusDMs: number[] = [];
    const minusDMs: number[] = [];

    // Calculate True Range, +DM, and -DM
    for (let i = 1; i < ohlcv.length; i++) {
      const current = ohlcv[i];
      const previous = ohlcv[i - 1];

      // True Range
      const tr = Math.max(
        current.high - current.low,
        Math.abs(current.high - previous.close),
        Math.abs(current.low - previous.close)
      );
      trueRanges.push(tr);

      // Directional Movement
      const upMove = current.high - previous.high;
      const downMove = previous.low - current.low;

      const plusDM = upMove > downMove && upMove > 0 ? upMove : 0;
      const minusDM = downMove > upMove && downMove > 0 ? downMove : 0;

      plusDMs.push(plusDM);
      minusDMs.push(minusDM);
    }

    if (trueRanges.length < period) return 0;

    // Calculate smoothed averages
    let atr = trueRanges.slice(0, period).reduce((sum, tr) => sum + tr, 0) / period;
    let plusDI = plusDMs.slice(0, period).reduce((sum, dm) => sum + dm, 0) / period;
    let minusDI = minusDMs.slice(0, period).reduce((sum, dm) => sum + dm, 0) / period;

    // Calculate DI+ and DI-
    plusDI = (plusDI / atr) * 100;
    minusDI = (minusDI / atr) * 100;

    // Calculate DX
    const dx = Math.abs(plusDI - minusDI) / (plusDI + minusDI) * 100;

    // For simplicity, return DX as ADX approximation
    // In a full implementation, ADX would be a smoothed average of DX values
    return Math.round(dx * 100) / 100;
  }
}
