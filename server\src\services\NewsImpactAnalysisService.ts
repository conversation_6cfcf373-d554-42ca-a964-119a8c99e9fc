import { GoogleGenerativeAI } from '@google/generative-ai';
import { GlobalNewsArticle } from './GlobalNewsService';
import { logger } from '../utils/logger';

export interface MarketImpactAnalysis {
  impactScore: number; // 0-100
  impactDirection: 'positive' | 'negative' | 'neutral';
  timeframe: 'immediate' | 'short_term' | 'medium_term' | 'long_term';
  confidence: number; // 0-100
  affectedSectors: SectorImpact[];
  affectedCompanies: CompanyImpact[];
  reasoning: string;
  tradingOpportunities: TradingOpportunity[];
  riskFactors: string[];
  catalysts: string[];
}

export interface SectorImpact {
  sector: string;
  impactScore: number; // -100 to 100
  reasoning: string;
  confidence: number;
  timeframe: string;
}

export interface CompanyImpact {
  companyName: string;
  ticker?: string;
  impactScore: number; // -100 to 100
  reasoning: string;
  confidence: number;
  sector: string;
}

export interface TradingOpportunity {
  type: 'long' | 'short' | 'pairs_trade' | 'sector_rotation';
  tickers: string[];
  reasoning: string;
  expectedReturn: number;
  riskLevel: 'low' | 'medium' | 'high';
  timeframe: string;
  confidence: number;
}

export class NewsImpactAnalysisService {
  private genAI: GoogleGenerativeAI;
  private model: any;

  constructor(apiKey: string) {
    if (!apiKey) {
      throw new Error('Google AI API key is required');
    }
    this.genAI = new GoogleGenerativeAI(apiKey);
    this.model = this.genAI.getGenerativeModel({ model: 'gemma-3-27b-it' });
  }

  /**
   * Analyze market impact of news article
   */
  public async analyzeMarketImpact(article: GlobalNewsArticle): Promise<MarketImpactAnalysis> {
    try {
      const prompt = this.buildAnalysisPrompt(article);
      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();

      // Log the raw response from the AI for debugging
      logger.info('Raw AI response received', { 
        title: article.title.substring(0, 100),
        rawText: text 
      });

      const analysis = this.parseAnalysisResponse(text);
      
      if (analysis.confidence > 0) {
        logger.info('Market impact analysis completed successfully', {
          title: article.title.substring(0, 100),
          impactScore: analysis.impactScore,
          affectedSectors: analysis.affectedSectors.length,
          opportunities: analysis.tradingOpportunities.length
        });
      } else {
        logger.warn('Market impact analysis returned fallback data', {
          title: article.title.substring(0, 100)
        });
      }

      return analysis;
    } catch (error) {
      logger.error('Error analyzing market impact:', error);
      return this.getFallbackAnalysis();
    }
  }

  /**
   * Build comprehensive analysis prompt
   */
  private buildAnalysisPrompt(article: GlobalNewsArticle): string {
    return `
You are a professional financial analyst and market strategist with expertise in global markets, sector analysis, and trading strategies. Analyze the following news article for its potential market impact and trading opportunities.

**News Article:**
Title: ${article.title}
Content: ${article.content}
Source: ${article.source}
Published: ${article.publishedAt.toISOString()}
Category: ${article.category}

**Analysis Framework:**
Evaluate this news through multiple financial lenses, focusing on quantifiable impacts and actionable trading ideas.

1. **Market Impact Assessment**
   - Immediate market reaction potential (0-24 hours)
   - Short-term impact (1-7 days)
   - Medium-term implications (1-4 weeks)
   - Long-term structural changes (1-12 months)

2. **Sector Analysis**
   - Which sectors will be most affected (positively/negatively)?
   - Supply chain implications
   - Regulatory impact
   - Competitive dynamics changes

3. **Company-Specific Impact**
   - Identify specific companies likely to be affected
   - Provide stock tickers where possible
   - Assess direct vs indirect impact
   - Consider market cap and liquidity

4. **Trading Opportunities**
   - Long/short opportunities
   - Pairs trading possibilities
   - Sector rotation plays
   - Options strategies
   - Risk/reward assessment

5. **Risk Factors**
   - What could go wrong with the analysis?
   - Market sentiment risks
   - Execution risks
   - Timing risks

**Sector Categories to Consider:**
- Technology (Software, Hardware, Semiconductors, AI/ML)
- Healthcare (Pharmaceuticals, Biotechnology, Medical Devices)
- Energy (Oil & Gas, Renewable Energy, Utilities)
- Financial Services (Banks, Insurance, Fintech)
- Consumer (Retail, Consumer Goods, Automotive)
- Industrial (Manufacturing, Aerospace, Defense)
- Real Estate (REITs, Construction, Materials)
- Telecommunications (Telecom, Media, Entertainment)
- Agriculture (Food, Beverages, Agricultural Products)
- Transportation (Airlines, Shipping, Logistics)

**Response Format:**
Provide your analysis *only* in a single, valid JSON object enclosed in \`\`\`json ... \`\`\`. Do not include any text or explanations outside of the JSON structure. Ensure the JSON is syntactically correct with no trailing commas.

\`\`\`json
{
  "impactScore": 0-100,
  "impactDirection": "positive|negative|neutral",
  "timeframe": "immediate|short_term|medium_term|long_term",
  "confidence": 0-100,
  "affectedSectors": [
    {
      "sector": "sector_name",
      "impactScore": -100 to 100,
      "reasoning": "detailed_reasoning",
      "confidence": 0-100,
      "timeframe": "timeframe"
    }
  ],
  "affectedCompanies": [
    {
      "companyName": "company_name",
      "ticker": "TICKER",
      "impactScore": -100 to 100,
      "reasoning": "detailed_reasoning",
      "confidence": 0-100,
      "sector": "sector_name"
    }
  ],
  "reasoning": "comprehensive_analysis_explanation",
  "tradingOpportunities": [
    {
      "type": "long|short|pairs_trade|sector_rotation",
      "tickers": ["TICKER1", "TICKER2"],
      "reasoning": "opportunity_explanation",
      "expectedReturn": "percentage",
      "riskLevel": "low|medium|high",
      "timeframe": "timeframe",
      "confidence": 0-100
    }
  ],
  "riskFactors": ["risk1", "risk2", "risk3"],
  "catalysts": ["catalyst1", "catalyst2", "catalyst3"]
}
\`\`\`

**Important Guidelines:**
- Be specific with ticker symbols when possible and ensure they are valid, publicly-traded companies.
- Consider both direct and indirect impacts.
- Account for market sentiment and behavioral factors.
- Assess liquidity and tradability of opportunities.
- Consider correlation effects and portfolio implications.
- Factor in current market conditions and volatility.
- Provide actionable insights for professional traders.
- Do not output any text before or after the JSON block.
`;
  }

  /**
   * Parse AI analysis response
   */
  private parseAnalysisResponse(text: string): MarketImpactAnalysis {
    try {
      const jsonMatch = text.match(/```json([\s\S]*?)```/);
      if (jsonMatch && jsonMatch[1]) {
        const jsonString = jsonMatch[1].trim();
        const data = JSON.parse(jsonString);
        
        return {
          impactScore: data.impactScore || 0,
          impactDirection: data.impactDirection || 'neutral',
          timeframe: data.timeframe || 'short_term',
          confidence: data.confidence || 50,
          affectedSectors: data.affectedSectors || [],
          affectedCompanies: data.affectedCompanies || [],
          reasoning: data.reasoning || 'No analysis available',
          tradingOpportunities: data.tradingOpportunities || [],
          riskFactors: data.riskFactors || [],
          catalysts: data.catalysts || []
        };
      }

      // Fallback for cases where the AI doesn't use the markdown block
      const fallbackJsonMatch = text.match(/\{[\s\S]*\}/);
      if (fallbackJsonMatch) {
        logger.warn('Parsing fallback used; AI did not provide markdown JSON block.');
        const data = JSON.parse(fallbackJsonMatch[0]);
        return {
          impactScore: data.impactScore || 0,
          impactDirection: data.impactDirection || 'neutral',
          timeframe: data.timeframe || 'short_term',
          confidence: data.confidence || 50,
          affectedSectors: data.affectedSectors || [],
          affectedCompanies: data.affectedCompanies || [],
          reasoning: data.reasoning || 'No analysis available',
          tradingOpportunities: data.tradingOpportunities || [],
          riskFactors: data.riskFactors || [],
          catalysts: data.catalysts || []
        };
      }

      logger.error('Could not find or parse JSON in AI response', { responseText: text });
      return this.getFallbackAnalysis();
    } catch (error) {
      const e = error as Error;
      logger.error('Error parsing analysis response: ' + e.message, { 
        stack: e.stack,
        responseText: text 
      });
      return this.getFallbackAnalysis();
    }
  }

  /**
   * Fallback analysis for errors
   */
  private getFallbackAnalysis(): MarketImpactAnalysis {
    return {
      impactScore: 0,
      impactDirection: 'neutral',
      timeframe: 'short_term',
      confidence: 0,
      affectedSectors: [],
      affectedCompanies: [],
      reasoning: 'Analysis failed, unable to determine market impact',
      tradingOpportunities: [],
      riskFactors: ['Analysis uncertainty'],
      catalysts: []
    };
  }

  /**
   * Batch analyze multiple articles
   */
  public async batchAnalyzeArticles(
    articles: GlobalNewsArticle[],
    maxConcurrent: number = 5
  ): Promise<(GlobalNewsArticle & { analysis: MarketImpactAnalysis })[]> {
    const results: (GlobalNewsArticle & { analysis: MarketImpactAnalysis })[] = [];
    
    // Process articles in batches to avoid rate limits
    for (let i = 0; i < articles.length; i += maxConcurrent) {
      const batch = articles.slice(i, i + maxConcurrent);
      const batchPromises = batch.map(async (article) => {
        const analysis = await this.analyzeMarketImpact(article);
        return { ...article, analysis };
      });

      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);

      // Add delay between batches to respect rate limits
      if (i + maxConcurrent < articles.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    return results;
  }

  /**
   * Filter high-impact opportunities
   */
  public filterHighImpactOpportunities(
    analyzedArticles: (GlobalNewsArticle & { analysis: MarketImpactAnalysis })[],
    minImpactScore: number = 60,
    minConfidence: number = 70
  ): TradingOpportunity[] {
    const opportunities: TradingOpportunity[] = [];

    analyzedArticles.forEach(article => {
      if (article.analysis.impactScore >= minImpactScore && 
          article.analysis.confidence >= minConfidence) {
        opportunities.push(...article.analysis.tradingOpportunities.filter(
          opp => opp.confidence >= minConfidence
        ));
      }
    });

    // Remove duplicates and sort by expected return
    const uniqueOpportunities = opportunities.filter((opp, index, self) => 
      index === self.findIndex(o => 
        o.type === opp.type && 
        JSON.stringify(o.tickers.sort()) === JSON.stringify(opp.tickers.sort())
      )
    );

    return uniqueOpportunities.sort((a, b) => b.expectedReturn - a.expectedReturn);
  }

  /**
   * Get sector impact summary
   */
  public getSectorImpactSummary(
    analyzedArticles: (GlobalNewsArticle & { analysis: MarketImpactAnalysis })[]
  ): { [sector: string]: { totalImpact: number; articleCount: number; avgConfidence: number } } {
    const sectorSummary: { [sector: string]: { totalImpact: number; articleCount: number; avgConfidence: number } } = {};

    analyzedArticles.forEach(article => {
      article.analysis.affectedSectors.forEach(sectorImpact => {
        if (!sectorSummary[sectorImpact.sector]) {
          sectorSummary[sectorImpact.sector] = {
            totalImpact: 0,
            articleCount: 0,
            avgConfidence: 0
          };
        }

        sectorSummary[sectorImpact.sector].totalImpact += sectorImpact.impactScore;
        sectorSummary[sectorImpact.sector].articleCount += 1;
        sectorSummary[sectorImpact.sector].avgConfidence += sectorImpact.confidence;
      });
    });

    // Calculate averages
    Object.keys(sectorSummary).forEach(sector => {
      const summary = sectorSummary[sector];
      summary.avgConfidence = summary.avgConfidence / summary.articleCount;
    });

    return sectorSummary;
  }

  /**
   * Extract unique tickers from analysis
   */
  public extractTickers(
    analyzedArticles: (GlobalNewsArticle & { analysis: MarketImpactAnalysis })[]
  ): { ticker: string; impactScore: number; confidence: number; opportunities: number }[] {
    const tickerMap = new Map<string, { impactScore: number; confidence: number; opportunities: number; count: number }>();

    analyzedArticles.forEach(article => {
      // From affected companies
      article.analysis.affectedCompanies.forEach(company => {
        if (company.ticker) {
          const existing = tickerMap.get(company.ticker) || { impactScore: 0, confidence: 0, opportunities: 0, count: 0 };
          existing.impactScore += company.impactScore;
          existing.confidence += company.confidence;
          existing.count += 1;
          tickerMap.set(company.ticker, existing);
        }
      });

      // From trading opportunities
      article.analysis.tradingOpportunities.forEach(opportunity => {
        opportunity.tickers.forEach(ticker => {
          const existing = tickerMap.get(ticker) || { impactScore: 0, confidence: 0, opportunities: 0, count: 0 };
          existing.opportunities += 1;
          tickerMap.set(ticker, existing);
        });
      });
    });

    // Convert to array and calculate averages
    return Array.from(tickerMap.entries()).map(([ticker, data]) => ({
      ticker,
      impactScore: data.count > 0 ? data.impactScore / data.count : 0,
      confidence: data.count > 0 ? data.confidence / data.count : 0,
      opportunities: data.opportunities
    })).sort((a, b) => Math.abs(b.impactScore) - Math.abs(a.impactScore));
  }
}
