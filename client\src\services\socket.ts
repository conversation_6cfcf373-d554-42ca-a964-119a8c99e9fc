import { io, Socket } from 'socket.io-client';

const URL = 'http://localhost:3001';

class SocketService {
  private socket: Socket;

  constructor() {
    this.socket = io(URL, {
      autoConnect: false,
      transports: ['websocket'],
    });

    this.socket.on('connect', () => {
      console.log('Connected to WebSocket server');
    });

    this.socket.on('disconnect', () => {
      console.log('Disconnected from WebSocket server');
    });
  }

  public connect(): void {
    this.socket.connect();
  }

  public disconnect(): void {
    this.socket.disconnect();
  }

  public on(event: string, listener: (data: any) => void): void {
    this.socket.on(event, listener);
  }

  public off(event: string, listener?: (data: any) => void): void {
    this.socket.off(event, listener);
  }
}

export const socketService = new SocketService(); 