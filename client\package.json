{"name": "trading-bot-client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-slot": "^1.2.3", "axios": "^1.6.2", "browserslist": "^4.25.0", "caniuse-lite": "^1.0.30001724", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.18.1", "lightweight-charts": "^4.1.3", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-query": "^3.39.3", "react-router-dom": "^6.20.1", "recharts": "^2.8.0", "socket.io-client": "^4.8.1", "tailwind-merge": "^2.6.0"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.31", "tailwindcss": "^3.4.17", "typescript": "^5.2.2", "vite": "^5.0.0"}}