import dotenv from 'dotenv';
dotenv.config();

import mongoose from 'mongoose';
import { TickerDataModel } from '../models/TickerData';
import { SETTINGS } from '../config/settings';

const sampleData = [
  {
    ticker: 'XOM',
    currentPrice: 114.70,
    technicalIndicators: {
      rsi: 71.4, // Overbought - should trigger bearish signal
      macd: { macd: -0.050, signal: 0.149, histogram: -0.199 },
      bollingerBands: { upper: 117.86, middle: 114.70, lower: 111.54 },
      sma: { short: 115.47, long: 112.85, crossover: 'bullish' },
      atr: 2.23
    },
    news: [
      {
        title: 'Exxon Mobil Reports Strong Q4 Earnings',
        content: 'Exxon Mobil Corporation reported strong fourth quarter earnings...',
        url: 'https://example.com/news1',
        source: 'Reuters',
        publishedAt: new Date(),
        sentiment: {
          sentiment: 'Positive',
          confidence: 'High',
          summary: 'Strong earnings report with positive outlook',
          shortTermImpact: true,
          reasoning: 'Earnings beat expectations'
        }
      }
    ],
    recommendation: {
      ticker: 'XOM',
      action: 'Sell', // Should be Sell due to overbought RSI + positive sentiment
      confidence: 'Medium',
      technicalSignals: {
        rsi: 'bearish',
        macd: 'bearish', 
        bollingerBands: 'neutral',
        sma: 'bullish'
      },
      sentiment: 'Positive',
      reasoning: 'Bearish technical signal with strong positive sentiment.',
      riskManagement: {
        stopLoss: 110.24,
        takeProfit: 123.62,
        positionSize: 1000,
        maxRisk: 0.02
      },
      timestamp: new Date()
    },
    lastUpdated: new Date()
  },
  {
    ticker: 'CVX',
    currentPrice: 149.55,
    technicalIndicators: {
      rsi: 68.8, // Close to overbought
      macd: { macd: 0.090, signal: 0.149, histogram: -0.059 },
      bollingerBands: { upper: 152.86, middle: 149.55, lower: 146.24 },
      sma: { short: 150.47, long: 147.85, crossover: 'bullish' },
      atr: 2.90
    },
    news: [
      {
        title: 'Chevron Announces Dividend Increase',
        content: 'Chevron Corporation announced a dividend increase...',
        url: 'https://example.com/news2',
        source: 'Bloomberg',
        publishedAt: new Date(),
        sentiment: {
          sentiment: 'Positive',
          confidence: 'Medium',
          summary: 'Dividend increase signals strong cash flow',
          shortTermImpact: false,
          reasoning: 'Positive for long-term investors'
        }
      }
    ],
    recommendation: {
      ticker: 'CVX',
      action: 'Buy', // Should be Buy due to bullish signals + positive sentiment
      confidence: 'High',
      technicalSignals: {
        rsi: 'neutral',
        macd: 'bearish',
        bollingerBands: 'neutral',
        sma: 'bullish'
      },
      sentiment: 'Positive',
      reasoning: 'Bullish technical signal with strong positive sentiment.',
      riskManagement: {
        stopLoss: 143.75,
        takeProfit: 161.15,
        positionSize: 1000,
        maxRisk: 0.02
      },
      timestamp: new Date()
    },
    lastUpdated: new Date()
  },
  {
    ticker: 'LMT',
    currentPrice: 470.56,
    technicalIndicators: {
      rsi: 48.9, // Neutral
      macd: { macd: -2.50, signal: -1.49, histogram: -1.01 },
      bollingerBands: { upper: 485.86, middle: 470.56, lower: 455.26 },
      sma: { short: 472.47, long: 465.85, crossover: 'bullish' },
      atr: 12.56
    },
    news: [
      {
        title: 'Lockheed Martin Wins Defense Contract',
        content: 'Lockheed Martin secured a major defense contract...',
        url: 'https://example.com/news3',
        source: 'Defense News',
        publishedAt: new Date(),
        sentiment: {
          sentiment: 'Positive',
          confidence: 'High',
          summary: 'Major contract win boosts revenue outlook',
          shortTermImpact: true,
          reasoning: 'Contract provides revenue visibility'
        }
      }
    ],
    recommendation: {
      ticker: 'LMT',
      action: 'Buy', // Should be Buy due to positive sentiment + some bullish signals
      confidence: 'Medium',
      technicalSignals: {
        rsi: 'neutral',
        macd: 'bearish',
        bollingerBands: 'neutral',
        sma: 'bullish'
      },
      sentiment: 'Positive',
      reasoning: 'Bullish technical signal with strong positive sentiment.',
      riskManagement: {
        stopLoss: 445.44,
        takeProfit: 520.80,
        positionSize: 1000,
        maxRisk: 0.02
      },
      timestamp: new Date()
    },
    lastUpdated: new Date()
  }
];

async function initializeData() {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(SETTINGS.mongodbUri);
    console.log('Connected to MongoDB');

    console.log('Clearing existing data...');
    await TickerDataModel.deleteMany({});

    console.log('Inserting sample data...');
    await TickerDataModel.insertMany(sampleData);

    console.log('Sample data inserted successfully!');
    console.log(`Inserted ${sampleData.length} ticker records`);

    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  } catch (error) {
    console.error('Error initializing data:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  initializeData();
}
