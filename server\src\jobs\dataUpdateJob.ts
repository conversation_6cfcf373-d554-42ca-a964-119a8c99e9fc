import cron from 'node-cron';
import { SETTINGS } from '../config/settings';
import { DataService } from '../services/DataService';
import { NewsService } from '../services/NewsService';
import { TechnicalAnalysisService } from '../services/TechnicalAnalysisService';
import { SentimentAnalysisService } from '../services/SentimentAnalysisService';
import { RecommendationService } from '../services/RecommendationService';
import { TickerManagementService } from '../services/TickerManagementService';
import { TickerDataModel } from '../models/TickerData';
import { LogEntryModel } from '../models/LogEntry';
import { logger } from '../utils/logger';

export class DataUpdateJob {
  private dataService: DataService;
  private newsService: NewsService;
  private techAnalysisService: TechnicalAnalysisService;
  private sentimentAnalysisService: SentimentAnalysisService;
  private recommendationService: RecommendationService;

  constructor(sentimentAnalysisService: SentimentAnalysisService) {
    this.dataService = new DataService();
    this.newsService = new NewsService();
    this.techAnalysisService = new TechnicalAnalysisService();
    this.sentimentAnalysisService = sentimentAnalysisService;
    this.recommendationService = new RecommendationService();
  }

  /**
   * Start the cron job to update data periodically
   */
  public start() {
    // Run every 15 minutes during market hours (Mon-Fri)
    cron.schedule(SETTINGS.updateInterval, () => {
      logger.info('Starting scheduled data update...');
      this.updateAllTickerData();
    }, {
      scheduled: true,
      timezone: 'America/New_York'
    });
  }

  /**
   * Update data for all configured tickers
   */
  public async updateAllTickerData() {
    try {
      const tickerManager = TickerManagementService.getInstance();
      const activeTickers = tickerManager.getActiveTickers();

      if (activeTickers.length === 0) {
        logger.warn('No active tickers found for data update');
        return;
      }

      for (const ticker of activeTickers) {
        await this.updateTicker(ticker);
      }
      logger.info('All ticker data updated successfully.', { tickerCount: activeTickers.length });
    } catch (error) {
      logger.error('Error updating all ticker data:', error);
    }
  }

  /**
   * Update data for a single ticker
   */
  private async updateTicker(ticker: string) {
    try {
      // 1. Fetch financial data
      const ohlcv = await this.dataService.fetchHistoricalData(ticker);
      if (ohlcv.length === 0) {
        throw new Error('No historical data fetched');
      }
      const currentPrice = await this.dataService.fetchCurrentPrice(ticker);

      // 2. Calculate technical indicators
      const technicalIndicators = this.techAnalysisService.calculateIndicators(ohlcv);

      // 3. Fetch news and analyze sentiment
      const newsArticles = await this.newsService.fetchNews(ticker);
      const sentimentResult = await this.sentimentAnalysisService.analyzeMultipleArticles(newsArticles, ticker);

      // 4. Generate recommendation
      const recommendation = this.recommendationService.generateRecommendation(
        ticker,
        currentPrice,
        technicalIndicators,
        {
          sentiment: sentimentResult.overallSentiment,
          confidence: sentimentResult.averageConfidence,
          summary: sentimentResult.summary,
          shortTermImpact: sentimentResult.articlesWithSentiment.some(a => a.sentiment.shortTermImpact),
          reasoning: sentimentResult.summary
        }
      );

      // 5. Save data to database
      await TickerDataModel.findOneAndUpdate(
        { ticker },
        {
          currentPrice,
          technicalIndicators,
          news: sentimentResult.articlesWithSentiment,
          recommendation,
          lastUpdated: new Date()
        },
        { upsert: true, new: true }
      );

      // 6. Log the update
      await this.logUpdate(ticker, 'success');
    } catch (error) {
      logger.error(`Failed to update data for ${ticker}:`, error);
      await this.logUpdate(ticker, 'error', { error: (error as Error).message });
    }
  }

  /**
   * Log the update status
   */
  private async logUpdate(ticker: string, status: 'success' | 'error', data: any = {}) {
    try {
      const logEntry = new LogEntryModel({
        logId: `${ticker}-${Date.now()}`,
        type: 'technical_analysis',
        ticker,
        message: `Data update ${status} for ${ticker}`,
        data,
        timestamp: new Date()
      });
      await logEntry.save();
    } catch (error) {
      logger.error('Error saving log entry:', error);
    }
  }
} 