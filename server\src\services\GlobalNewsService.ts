import axios from 'axios';
import { logger } from '../utils/logger';

export interface GlobalNewsArticle {
  title: string;
  content: string;
  source: string;
  publishedAt: Date;
  url: string;
  category: string;
  country: string;
  language: string;
  relevanceScore: number;
}

export interface NewsSource {
  name: string;
  url: string;
  apiKey?: string;
  credibilityScore: number; // 1-10
  categories: string[];
}

export class GlobalNewsService {
  private newsSources: NewsSource[];
  private newsApiKey: string;

  constructor(newsApiKey: string) {
    this.newsSources = [];
    this.newsApiKey = newsApiKey;
    this.initializeNewsSources();
  }

  private initializeNewsSources(): void {
    this.newsSources = [
      {
        name: 'Reuters',
        url: 'https://newsapi.org/v2/everything',
        credibilityScore: 9,
        categories: ['general', 'business', 'technology', 'health']
      },
      {
        name: 'Associated Press',
        url: 'https://newsapi.org/v2/everything',
        credibilityScore: 9,
        categories: ['general', 'business', 'technology']
      },
      {
        name: 'BBC News',
        url: 'https://newsapi.org/v2/everything',
        credibilityScore: 8,
        categories: ['general', 'business', 'technology', 'health']
      },
      {
        name: 'CNN',
        url: 'https://newsapi.org/v2/everything',
        credibilityScore: 7,
        categories: ['general', 'business', 'technology']
      },
      {
        name: 'The Guardian',
        url: 'https://newsapi.org/v2/everything',
        credibilityScore: 8,
        categories: ['general', 'business', 'technology', 'health']
      },
      {
        name: 'Financial Times',
        url: 'https://newsapi.org/v2/everything',
        credibilityScore: 9,
        categories: ['business', 'general']
      },
      {
        name: 'The Washington Post',
        url: 'https://newsapi.org/v2/everything',
        credibilityScore: 9,
        categories: ['general', 'politics']
      },
      {
        name: 'NPR',
        url: 'https://newsapi.org/v2/everything',
        credibilityScore: 8,
        categories: ['general', 'health', 'science']
      },
      {
        name: 'Forbes',
        url: 'https://newsapi.org/v2/everything',
        credibilityScore: 7,
        categories: ['business', 'technology']
      },
      {
        name: 'Business Insider',
        url: 'https://newsapi.org/v2/everything',
        credibilityScore: 7,
        categories: ['business', 'technology', 'general']
      },
      {
        name: 'CNBC',
        url: 'https://newsapi.org/v2/everything',
        credibilityScore: 8,
        categories: ['business', 'technology']
      },
      {
        name: 'Axios',
        url: 'https://newsapi.org/v2/everything',
        credibilityScore: 8,
        categories: ['general', 'politics', 'technology']
      },
      {
        name: 'Politico',
        url: 'https://newsapi.org/v2/everything',
        credibilityScore: 8,
        categories: ['general', 'politics']
      }
    ];
  }

  /**
   * Fetch global news from multiple sources
   */
  public async fetchGlobalNews(
    categories: string[] = ['general', 'technology', 'health', 'science'],
    hoursBack: number = 24,
    maxArticles: number = 100
  ): Promise<GlobalNewsArticle[]> {
    try {
      const allArticles: GlobalNewsArticle[] = [];
      const fromDate = new Date(Date.now() - hoursBack * 60 * 60 * 1000);

      // Fetch from NewsAPI for specified categories
      const newsApiArticles = await this.fetchFromNewsAPI(categories, fromDate, maxArticles);
      allArticles.push(...newsApiArticles);

      // Deduplicate articles based on title
      const uniqueArticles = allArticles.filter(
        (article, index, self) =>
          index === self.findIndex(a => a.title === article.title)
      );

      // Sort by relevance and credibility
      uniqueArticles.sort((a, b) => {
        const scoreA = a.relevanceScore * this.getSourceCredibility(a.source);
        const scoreB = b.relevanceScore * this.getSourceCredibility(b.source);
        return scoreB - scoreA;
      });

      logger.info('Global news fetched successfully', {
        totalArticles: uniqueArticles.length,
        sources: [...new Set(uniqueArticles.map(a => a.source))],
        categories: [...new Set(uniqueArticles.map(a => a.category))]
      });

      return uniqueArticles.slice(0, maxArticles);
    } catch (error) {
      logger.error('Error fetching global news:', error);
      throw error;
    }
  }

  /**
   * Fetch news from NewsAPI
   */
  private async fetchFromNewsAPI(
    categories: string[],
    fromDate: Date,
    maxArticles: number
  ): Promise<GlobalNewsArticle[]> {
    const articles: GlobalNewsArticle[] = [];
    const articlesPerCategory = Math.ceil(maxArticles / categories.length);

    for (const category of categories) {
      try {
        const response = await axios.get('https://newsapi.org/v2/top-headlines', {
          params: {
            apiKey: this.newsApiKey,
            language: 'en',
            pageSize: Math.min(100, articlesPerCategory),
            category: category,
            from: fromDate.toISOString()
          }
        });

        if (response.data.articles) {
          for (const article of response.data.articles) {
            if (article.title && article.description) {
              articles.push({
                title: article.title,
                content: article.description + (article.content || ''),
                source: article.source.name,
                publishedAt: new Date(article.publishedAt),
                url: article.url,
                category: category,
                country: 'us', // This could be improved for more global coverage
                language: 'en',
                relevanceScore: this.calculateRelevanceScore(article.title, article.description)
              });
            }
          }
        }
      } catch (error) {
        logger.warn(`Failed to fetch news for category '${category}' from NewsAPI`, {
          error: (error as Error).message
        });
      }
    }

    return articles;
  }

  /**
   * Calculate relevance score for market impact analysis
   */
  private calculateRelevanceScore(title: string, content: string): number {
    const text = (title + ' ' + content).toLowerCase();
    let score = 0.5; // Base score

    // Market impact keywords
    const highImpactKeywords = [
      'federal reserve', 'interest rate', 'inflation', 'gdp', 'unemployment',
      'trade war', 'sanctions', 'oil price', 'supply chain', 'recession',
      'merger', 'acquisition', 'ipo', 'earnings', 'bankruptcy',
      'regulation', 'policy', 'election', 'war', 'conflict'
    ];

    const mediumImpactKeywords = [
      'technology', 'innovation', 'breakthrough', 'patent', 'lawsuit',
      'partnership', 'investment', 'funding', 'expansion', 'layoffs',
      'strike', 'protest', 'weather', 'disaster', 'cyber attack'
    ];

    // Check for high impact keywords
    for (const keyword of highImpactKeywords) {
      if (text.includes(keyword)) {
        score += 0.3;
      }
    }

    // Check for medium impact keywords
    for (const keyword of mediumImpactKeywords) {
      if (text.includes(keyword)) {
        score += 0.2;
      }
    }

    // Boost score for specific geopolitical keywords
    const geoKeywords = [
        'geopolitical', 'election', 'government', 'protest', 'unrest', 'uprising',
        'summit', 'treaty', 'diplomacy', 'foreign policy'
    ];
    for (const keyword of geoKeywords) {
        if (text.includes(keyword)) {
            score += 0.25;
        }
    }

    // Boost score for specific sectors
    const sectorKeywords = {
      'energy': ['oil', 'gas', 'renewable', 'solar', 'wind', 'nuclear', 'opec'],
      'technology': ['ai', 'artificial intelligence', 'chip', 'semiconductor', 'software'],
      'healthcare': ['drug', 'vaccine', 'medical', 'pharmaceutical', 'fda', 'pandemic'],
      'finance': ['bank', 'credit', 'loan', 'mortgage', 'insurance', 'treasury'],
      'defense': ['military', 'defense', 'weapon', 'security', 'aerospace', 'nato']
    };

    for (const [sector, keywords] of Object.entries(sectorKeywords)) {
      for (const keyword of keywords) {
        if (text.includes(keyword)) {
          score += 0.1;
        }
      }
    }

    return Math.min(1.0, score);
  }

  /**
   * Get source credibility score
   */
  private getSourceCredibility(sourceName: string): number {
    const source = this.newsSources.find(s => 
      sourceName.toLowerCase().includes(s.name.toLowerCase())
    );
    return source ? source.credibilityScore / 10 : 0.5;
  }

  /**
   * Filter news by market relevance
   */
  public filterMarketRelevantNews(
    articles: GlobalNewsArticle[],
    minRelevanceScore: number = 0.5
  ): GlobalNewsArticle[] {
    return articles
      .filter(article => {
        const credibility = this.getSourceCredibility(article.source);
        return article.relevanceScore * credibility >= minRelevanceScore;
      })
      .sort((a, b) => b.relevanceScore - a.relevanceScore);
  }

  /**
   * Get news by category
   */
  public async getNewsByCategory(
    category: string,
    hoursBack: number = 24,
    maxArticles: number = 50
  ): Promise<GlobalNewsArticle[]> {
    try {
      const response = await axios.get('https://newsapi.org/v2/top-headlines', {
        params: {
          apiKey: this.newsApiKey,
          language: 'en',
          pageSize: maxArticles,
          category: category
        }
      });

      const articles: GlobalNewsArticle[] = [];
      
      if (response.data.articles) {
        for (const article of response.data.articles) {
          if (article.title && article.description) {
            articles.push({
              title: article.title,
              content: article.description + (article.content || ''),
              source: article.source.name,
              publishedAt: new Date(article.publishedAt),
              url: article.url,
              category: category,
              country: 'us',
              language: 'en',
              relevanceScore: this.calculateRelevanceScore(article.title, article.description)
            });
          }
        }
      }

      return articles;
    } catch (error) {
      logger.error(`Error fetching ${category} news:`, error);
      return [];
    }
  }

  /**
   * Search news by keywords
   */
  public async searchNews(
    keywords: string[],
    hoursBack: number = 24,
    maxArticles: number = 50
  ): Promise<GlobalNewsArticle[]> {
    try {
      const query = keywords.join(' OR ');
      const fromDate = new Date(Date.now() - hoursBack * 60 * 60 * 1000);

      const response = await axios.get('https://newsapi.org/v2/everything', {
        params: {
          apiKey: this.newsApiKey,
          q: query,
          language: 'en',
          sortBy: 'relevancy',
          pageSize: maxArticles,
          from: fromDate.toISOString()
        }
      });

      const articles: GlobalNewsArticle[] = [];
      
      if (response.data.articles) {
        for (const article of response.data.articles) {
          if (article.title && article.description) {
            articles.push({
              title: article.title,
              content: article.description + (article.content || ''),
              source: article.source.name,
              publishedAt: new Date(article.publishedAt),
              url: article.url,
              category: 'search',
              country: 'us',
              language: 'en',
              relevanceScore: this.calculateRelevanceScore(article.title, article.description)
            });
          }
        }
      }

      return articles;
    } catch (error) {
      logger.error('Error searching news:', error);
      return [];
    }
  }
}
