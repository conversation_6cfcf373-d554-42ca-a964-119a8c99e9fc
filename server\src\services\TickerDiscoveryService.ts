import { GlobalNewsService, GlobalNewsArticle } from './GlobalNewsService';
import { NewsImpactAnalysisService, MarketImpactAnalysis } from './NewsImpactAnalysisService';
import { logger, logDiscovery, logError, logWarn } from '../utils/logger';
import axios from 'axios';
import yahooFinance from 'yahoo-finance2';
import { getWebSocketService } from './WebSocketService';

export interface DiscoveredTicker {
  ticker: string;
  companyName: string;
  sector: string;
  marketCap: number;
  discoveryReason: string;
  impactScore: number;
  confidence: number;
  newsArticles: GlobalNewsArticle[];
  tradingOpportunities: number;
  riskLevel: 'low' | 'medium' | 'high';
  timeframe: string;
  discoveredAt: Date;
}

export interface DiscoveryConfig {
  minImpactScore: number;
  minConfidence: number;
  maxTickersPerRun: number;
  excludedSectors: string[];
  minMarketCap: number;
  newsLookbackHours: number;
  analysisCategories: string[];
}

export interface TickerValidation {
  ticker: string;
  isValid: boolean;
  companyName?: string;
  sector?: string;
  marketCap?: number;
  exchange?: string;
  currency?: string;
}

export class TickerDiscoveryService {
  private globalNewsService: GlobalNewsService;
  private newsImpactService: NewsImpactAnalysisService;
  private discoveryConfig: DiscoveryConfig;

  constructor(
    newsApiKey: string,
    geminiApiKey: string,
    config?: Partial<DiscoveryConfig>
  ) {
    this.globalNewsService = new GlobalNewsService(newsApiKey);
    this.newsImpactService = new NewsImpactAnalysisService(geminiApiKey);
    
    // Suppress verbose notices from yahoo-finance2
    yahooFinance.suppressNotices(['yahooSurvey']);

    this.discoveryConfig = {
      minImpactScore: 50,
      minConfidence: 65,
      maxTickersPerRun: 20,
      excludedSectors: ['Penny Stocks', 'OTC'],
      minMarketCap: *********, // $100M
      newsLookbackHours: 24,
      analysisCategories: ['general', 'world', 'nation', 'technology', 'science', 'health'],
      ...config
    };
  }

  /**
   * Main discovery process - find new tickers from global news
   */
  public async discoverNewTickers(): Promise<DiscoveredTicker[]> {
    const ws = getWebSocketService();
    try {
      logDiscovery('Start', 'Starting ticker discovery process', {
        lookbackHours: this.discoveryConfig.newsLookbackHours,
        categories: this.discoveryConfig.analysisCategories,
      });
      ws.emit('discovery_update', { stage: 'start', message: 'Starting ticker discovery process...' });

      // Step 1: Fetch global news
      ws.emit('discovery_update', { stage: 'news_fetch', message: 'Fetching global news...' });
      const newsArticles = await this.globalNewsService.fetchGlobalNews(
        this.discoveryConfig.analysisCategories,
        this.discoveryConfig.newsLookbackHours,
        100
      );

      // Step 2: Filter for market-relevant news
      ws.emit('discovery_update', { stage: 'news_filter', message: `Found ${newsArticles.length} articles. Filtering for market relevance...` });
      const relevantNews = this.globalNewsService.filterMarketRelevantNews(newsArticles);

      // Step 3: Analyze market impact
      ws.emit('discovery_update', { stage: 'news_analysis', message: `Found ${relevantNews.length} relevant articles. Analyzing for market impact...` });
      const analyzedArticles = await this.newsImpactService.batchAnalyzeArticles(relevantNews, 3);

      // Step 4: Extract tickers from analysis
      ws.emit('discovery_update', { stage: 'ticker_extraction', message: 'Extracting potential tickers from analysis...' });
      const extractedTickers = this.newsImpactService.extractTickers(analyzedArticles);

      // Step 5: Validate tickers
      ws.emit('discovery_update', { stage: 'ticker_validation', message: `Found ${extractedTickers.length} potential tickers. Validating...` });
      const validatedTickers = await this.validateTickers(
        extractedTickers.map(t => t.ticker)
      );

      // Step 6: Create discovered ticker objects
      ws.emit('discovery_update', { stage: 'ticker_creation', message: 'Creating discovered ticker objects...' });
      const discoveredTickers = await this.createDiscoveredTickers(
        validatedTickers,
        analyzedArticles,
        extractedTickers
      );

      // Step 7: Filter and rank
      ws.emit('discovery_update', { stage: 'ranking', message: 'Filtering and ranking final tickers...' });
      const filteredTickers = this.filterAndRankTickers(discoveredTickers);

      logDiscovery(
        'Complete',
        `Ticker discovery completed. Found ${filteredTickers.length} new tickers.`,
        {
          totalNews: newsArticles.length,
          relevantNews: relevantNews.length,
          extractedTickers: extractedTickers.length,
          validTickers: validatedTickers.filter(t => t.isValid).length,
          finalTickers: filteredTickers.length,
        }
      );
      ws.emit('discovery_update', { stage: 'complete', message: `Discovery complete. Found ${filteredTickers.length} new tickers.`, data: filteredTickers });

      return filteredTickers.slice(0, this.discoveryConfig.maxTickersPerRun);
    } catch (error) {
      logError('Discovery', 'discoverNewTickers', error as Error);
      ws.emit('discovery_error', { message: 'An error occurred during discovery.', error: (error as Error).message });
      throw error;
    }
  }

  /**
   * Validate ticker symbols using financial API
   */
  private async validateTickers(tickers: string[]): Promise<TickerValidation[]> {
    const validations: TickerValidation[] = [];
    const uniqueTickers = [...new Set(tickers)];

    for (const ticker of uniqueTickers) {
      try {
        // Use Alpha Vantage or similar API to validate ticker
        // For now, using a simple validation approach
        const validation = await this.validateSingleTicker(ticker);
        validations.push(validation);
        
        // Add delay to respect rate limits
        await new Promise(resolve => setTimeout(resolve, 200));
      } catch (error) {
        logWarn('Discovery', 'validateTickers', `Failed to validate ticker ${ticker}:`, error as any);
        validations.push({
          ticker,
          isValid: false
        });
      }
    }

    return validations;
  }

  /**
   * Validate a single ticker using Yahoo Finance API
   */
  private async validateSingleTicker(ticker: string): Promise<TickerValidation> {
    try {
      // Simple validation - check if ticker format is valid. Allow for extensions like .B or .A
      if (!/^[A-Z]{1,5}(\.[A-Z])?$/.test(ticker)) {
        return { ticker, isValid: false };
      }

      // Use Yahoo Finance API to validate ticker and get company info
      try {
        const summary = await yahooFinance.quoteSummary(ticker, {
          modules: ['assetProfile', 'price'],
        });

        if (summary && summary.price && summary.price.regularMarketPrice) {
          return {
            ticker,
            isValid: true,
            companyName: summary.price.longName || summary.price.shortName || `${ticker} Corporation`,
            sector: summary.assetProfile?.sector || 'Unknown',
            marketCap: summary.price.marketCap || 0,
            exchange: summary.price.exchangeName || 'Unknown',
            currency: summary.price.currency || 'USD',
          };
        }
      } catch (apiError) {
        // Fallback for tickers that might not have a summary but are valid (e.g. indices)
        if ((apiError as any).code !== '404') {
          try {
            const quote = await yahooFinance.quote(ticker);
            if (quote && quote.regularMarketPrice) {
              return {
                ticker,
                isValid: true,
                companyName: quote.longName || quote.shortName || `${ticker} Corporation`,
                sector: 'N/A',
                marketCap: quote.marketCap || 0,
                exchange: quote.exchange || 'Unknown',
                currency: quote.currency || 'USD',
              };
            }
          } catch (quoteError) {
             logWarn('Discovery', 'validateSingleTicker', `Yahoo Finance fallback quote failed for ${ticker}`, quoteError as any);
          }
        }
      }

      return { ticker, isValid: false };
    } catch (error) {
      logError('Discovery', 'validateSingleTicker', error as Error, { ticker });
      return { ticker, isValid: false };
    }
  }

  /**
   * Create discovered ticker objects
   */
  private async createDiscoveredTickers(
    validatedTickers: TickerValidation[],
    analyzedArticles: (GlobalNewsArticle & { analysis: MarketImpactAnalysis })[],
    extractedTickers: { ticker: string; impactScore: number; confidence: number; opportunities: number }[]
  ): Promise<DiscoveredTicker[]> {
    const discoveredTickers: DiscoveredTicker[] = [];

    for (const validation of validatedTickers) {
      if (!validation.isValid) continue;

      const extractedData = extractedTickers.find(t => t.ticker === validation.ticker);
      if (!extractedData) continue;

      // Find related news articles
      const relatedArticles = analyzedArticles.filter(article =>
        article.analysis.affectedCompanies.some(company => company.ticker === validation.ticker) ||
        article.analysis.tradingOpportunities.some(opp => opp.tickers.includes(validation.ticker))
      );

      // Determine discovery reason
      const discoveryReason = this.generateDiscoveryReason(relatedArticles, validation.ticker);

      // Assess risk level
      const riskLevel = this.assessRiskLevel(extractedData.impactScore, extractedData.confidence);

      // Determine timeframe
      const timeframe = this.determineTimeframe(relatedArticles);

      discoveredTickers.push({
        ticker: validation.ticker,
        companyName: validation.companyName || 'Unknown',
        sector: validation.sector || 'Unknown',
        marketCap: validation.marketCap || 0,
        discoveryReason,
        impactScore: extractedData.impactScore,
        confidence: extractedData.confidence,
        newsArticles: relatedArticles.map(a => ({
          title: a.title,
          content: a.content,
          source: a.source,
          publishedAt: a.publishedAt,
          url: a.url,
          category: a.category,
          country: a.country,
          language: a.language,
          relevanceScore: a.relevanceScore
        })),
        tradingOpportunities: extractedData.opportunities,
        riskLevel,
        timeframe,
        discoveredAt: new Date()
      });
    }

    return discoveredTickers;
  }

  /**
   * Filter and rank discovered tickers
   */
  private filterAndRankTickers(tickers: DiscoveredTicker[]): DiscoveredTicker[] {
    return tickers
      .filter(ticker => 
        Math.abs(ticker.impactScore) >= this.discoveryConfig.minImpactScore &&
        ticker.confidence >= this.discoveryConfig.minConfidence &&
        ticker.marketCap >= this.discoveryConfig.minMarketCap &&
        !this.discoveryConfig.excludedSectors.includes(ticker.sector)
      )
      .sort((a, b) => {
        // Sort by weighted score: impact * confidence * opportunities
        const scoreA = Math.abs(a.impactScore) * (a.confidence / 100) * (a.tradingOpportunities + 1);
        const scoreB = Math.abs(b.impactScore) * (b.confidence / 100) * (b.tradingOpportunities + 1);
        return scoreB - scoreA;
      });
  }

  /**
   * Generate discovery reason
   */
  private generateDiscoveryReason(
    articles: (GlobalNewsArticle & { analysis: MarketImpactAnalysis })[],
    ticker: string
  ): string {
    if (articles.length === 0) return 'Unknown discovery reason';

    const reasons = articles.map(article => {
      const companyImpact = article.analysis.affectedCompanies.find(c => c.ticker === ticker);
      if (companyImpact) {
        return `${article.title.substring(0, 100)}... (Impact: ${companyImpact.impactScore})`;
      }

      const opportunity = article.analysis.tradingOpportunities.find(opp => opp.tickers.includes(ticker));
      if (opportunity) {
        return `Trading opportunity: ${opportunity.type} (${article.title.substring(0, 80)}...)`;
      }

      return article.title.substring(0, 100) + '...';
    });

    return reasons[0]; // Return the first (most relevant) reason
  }

  /**
   * Assess risk level
   */
  private assessRiskLevel(impactScore: number, confidence: number): 'low' | 'medium' | 'high' {
    const riskScore = Math.abs(impactScore) * (1 - confidence / 100);
    
    if (riskScore < 20) return 'low';
    if (riskScore < 50) return 'medium';
    return 'high';
  }

  /**
   * Determine timeframe
   */
  private determineTimeframe(articles: (GlobalNewsArticle & { analysis: MarketImpactAnalysis })[]): string {
    if (articles.length === 0) return 'short_term';

    const timeframes = articles.map(a => a.analysis.timeframe);
    const timeframeCounts = timeframes.reduce((acc, tf) => {
      acc[tf] = (acc[tf] || 0) + 1;
      return acc;
    }, {} as { [key: string]: number });

    return Object.keys(timeframeCounts).reduce((a, b) => 
      timeframeCounts[a] > timeframeCounts[b] ? a : b
    );
  }

  /**
   * Get discovery statistics from database
   */
  public async getDiscoveryStats(): Promise<{
    totalNewsAnalyzed: number;
    tickersDiscovered: number;
    averageConfidence: number;
    topSectors: string[];
    riskDistribution: { [key: string]: number };
  }> {
    try {
      // Import models dynamically to avoid circular dependencies
      const { DiscoveredTickerModel } = await import('../models/DiscoveredTicker');
      const { LogEntryModel } = await import('../models/LogEntry');

      // Get total news analyzed from logs
      const newsAnalyzedLogs = await LogEntryModel.countDocuments({
        type: 'news_analysis',
        timestamp: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) } // Last 30 days
      });

      // Get discovered tickers
      const discoveredTickers = await DiscoveredTickerModel.find({
        discoveredAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) } // Last 30 days
      });

      // Calculate average confidence
      const totalConfidence = discoveredTickers.reduce((sum, ticker) => sum + ticker.confidence, 0);
      const averageConfidence = discoveredTickers.length > 0 ? Math.round(totalConfidence / discoveredTickers.length) : 0;

      // Get top sectors
      const sectorCounts: { [key: string]: number } = {};
      discoveredTickers.forEach(ticker => {
        sectorCounts[ticker.sector] = (sectorCounts[ticker.sector] || 0) + 1;
      });
      const topSectors = Object.entries(sectorCounts)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 4)
        .map(([sector]) => sector);

      // Get risk distribution
      const riskDistribution = { low: 0, medium: 0, high: 0 };
      discoveredTickers.forEach(ticker => {
        riskDistribution[ticker.riskLevel]++;
      });

      return {
        totalNewsAnalyzed: newsAnalyzedLogs || 0,
        tickersDiscovered: discoveredTickers.length,
        averageConfidence,
        topSectors: topSectors.length > 0 ? topSectors : ['Technology', 'Healthcare'],
        riskDistribution
      };
    } catch (error) {
      logger.error('Error fetching discovery stats:', error);
      // Return default stats if database query fails
      return {
        totalNewsAnalyzed: 0,
        tickersDiscovered: 0,
        averageConfidence: 0,
        topSectors: ['Technology', 'Healthcare'],
        riskDistribution: { low: 0, medium: 0, high: 0 }
      };
    }
  }
}
