import { logger } from '../utils/logger';

export interface ManagedTicker {
  ticker: string;
  companyName: string;
  sector: string;
  addedAt: Date;
  isActive: boolean;
  source: 'manual' | 'discovery' | 'default';
}

export class TickerManagementService {
  private static instance: TickerManagementService;
  private managedTickers: Map<string, ManagedTicker> = new Map();

  private constructor() {
    this.initializeDefaultTickers();
  }

  public static getInstance(): TickerManagementService {
    if (!TickerManagementService.instance) {
      TickerManagementService.instance = new TickerManagementService();
    }
    return TickerManagementService.instance;
  }

  /**
   * Initialize with default tickers for initial system operation
   */
  private initializeDefaultTickers(): void {
    const defaultTickers = [
      { ticker: 'LMT', companyName: 'Lockheed Martin Corporation', sector: 'Industrials' },
      { ticker: 'RTX', companyName: 'Raytheon Technologies Corporation', sector: 'Industrials' },
      { ticker: 'NOC', companyName: 'Northrop Grumman Corporation', sector: 'Industrials' },
      { ticker: 'XOM', companyName: 'Exxon Mobil Corporation', sector: 'Energy' },
      { ticker: 'CVX', companyName: 'Chevron Corporation', sector: 'Energy' },
      { ticker: 'GLD', companyName: 'SPDR Gold Trust', sector: 'Commodities' }
    ];

    defaultTickers.forEach(({ ticker, companyName, sector }) => {
      this.managedTickers.set(ticker, {
        ticker,
        companyName,
        sector,
        addedAt: new Date(),
        isActive: true,
        source: 'default'
      });
    });

    logger.info('Initialized default tickers', { count: defaultTickers.length });
  }

  /**
   * Get all active tickers
   */
  public getActiveTickers(): string[] {
    return Array.from(this.managedTickers.values())
      .filter(ticker => ticker.isActive)
      .map(ticker => ticker.ticker);
  }

  /**
   * Get all managed tickers with details
   */
  public getAllManagedTickers(): ManagedTicker[] {
    return Array.from(this.managedTickers.values());
  }

  /**
   * Add a new ticker from discovery
   */
  public addDiscoveredTicker(ticker: string, companyName: string, sector: string): boolean {
    if (this.managedTickers.has(ticker)) {
      logger.info(`Ticker ${ticker} already exists, updating info`);
      const existing = this.managedTickers.get(ticker)!;
      existing.companyName = companyName;
      existing.sector = sector;
      existing.isActive = true;
      return false; // Not a new ticker
    }

    this.managedTickers.set(ticker, {
      ticker,
      companyName,
      sector,
      addedAt: new Date(),
      isActive: true,
      source: 'discovery'
    });

    logger.info(`Added new discovered ticker: ${ticker}`, { companyName, sector });
    return true; // New ticker added
  }

  /**
   * Add a ticker manually
   */
  public addManualTicker(ticker: string, companyName: string, sector: string): boolean {
    if (this.managedTickers.has(ticker)) {
      logger.info(`Ticker ${ticker} already exists`);
      return false;
    }

    this.managedTickers.set(ticker, {
      ticker,
      companyName,
      sector,
      addedAt: new Date(),
      isActive: true,
      source: 'manual'
    });

    logger.info(`Added manual ticker: ${ticker}`, { companyName, sector });
    return true;
  }

  /**
   * Deactivate a ticker
   */
  public deactivateTicker(ticker: string): boolean {
    const managedTicker = this.managedTickers.get(ticker);
    if (!managedTicker) {
      return false;
    }

    managedTicker.isActive = false;
    logger.info(`Deactivated ticker: ${ticker}`);
    return true;
  }

  /**
   * Reactivate a ticker
   */
  public reactivateTicker(ticker: string): boolean {
    const managedTicker = this.managedTickers.get(ticker);
    if (!managedTicker) {
      return false;
    }

    managedTicker.isActive = true;
    logger.info(`Reactivated ticker: ${ticker}`);
    return true;
  }

  /**
   * Remove a ticker completely
   */
  public removeTicker(ticker: string): boolean {
    const removed = this.managedTickers.delete(ticker);
    if (removed) {
      logger.info(`Removed ticker: ${ticker}`);
    }
    return removed;
  }

  /**
   * Get ticker info
   */
  public getTickerInfo(ticker: string): ManagedTicker | undefined {
    return this.managedTickers.get(ticker);
  }

  /**
   * Load tickers from database (for persistence)
   */
  public async loadFromDatabase(): Promise<void> {
    try {
      const { TickerDataModel } = await import('../models/TickerData');
      const existingTickers = await TickerDataModel.find({}, 'ticker').lean();
      
      // Add existing tickers from database if not already managed
      for (const { ticker } of existingTickers) {
        if (!this.managedTickers.has(ticker)) {
          this.managedTickers.set(ticker, {
            ticker,
            companyName: `${ticker} Corporation`,
            sector: 'Unknown',
            addedAt: new Date(),
            isActive: true,
            source: 'manual'
          });
        }
      }

      logger.info('Loaded tickers from database', { 
        total: this.managedTickers.size,
        active: this.getActiveTickers().length 
      });
    } catch (error) {
      logger.error('Error loading tickers from database:', error);
    }
  }

  /**
   * Get statistics
   */
  public getStats(): {
    total: number;
    active: number;
    bySource: { [key: string]: number };
    bySector: { [key: string]: number };
  } {
    const tickers = Array.from(this.managedTickers.values());
    const bySource: { [key: string]: number } = {};
    const bySector: { [key: string]: number } = {};

    tickers.forEach(ticker => {
      bySource[ticker.source] = (bySource[ticker.source] || 0) + 1;
      bySector[ticker.sector] = (bySector[ticker.sector] || 0) + 1;
    });

    return {
      total: tickers.length,
      active: tickers.filter(t => t.isActive).length,
      bySource,
      bySector
    };
  }
}
