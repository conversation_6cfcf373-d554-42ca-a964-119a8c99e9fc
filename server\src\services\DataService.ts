import yahooFinance from 'yahoo-finance2';
import { OHLCV } from '../types';
import { logError, logger } from '../utils/logger';

export class DataService {
  /**
   * Fetch historical OHLCV data for a given ticker
   */
  public async fetchHistoricalData(ticker: string): Promise<OHLCV[]> {
    const startTime = Date.now();
    const operation = 'fetchHistoricalData';

    try {
      logger.info('Starting historical data fetch', {
        component: 'DataService',
        operation,
        ticker,
        startDate: '2023-01-01'
      });

      const startDate = new Date('2023-01-01');
      const queryOptions = {
        period1: startDate,
        interval: '1d' as const,
      };

      const result = await yahooFinance.historical(ticker, queryOptions);
      const duration = Date.now() - startTime;

      const mappedData = result.map((item: any) => ({
        timestamp: new Date(item.date),
        open: item.open,
        high: item.high,
        low: item.low,
        close: item.close,
        volume: item.volume
      }));

      logger.info('Successfully fetched historical data', {
        component: 'DataService',
        operation,
        ticker,
        duration,
        recordCount: result.length,
        dateRange: {
          from: mappedData[0]?.timestamp,
          to: mappedData[mappedData.length - 1]?.timestamp
        }
      });


      return mappedData;
    } catch (error) {
      const duration = Date.now() - startTime;
      logError('DataService', operation, error as Error, {
        ticker,
        duration,
        startDate: '2023-01-01'
      });
      return [];
    }
  }

  /**
   * Fetch current price for a given ticker
   */
  public async fetchCurrentPrice(ticker: string): Promise<number> {
    const startTime = Date.now();
    const operation = 'fetchCurrentPrice';

    try {
      logger.info('Starting current price fetch', {
        component: 'DataService',
        operation,
        ticker
      });

      const quote = await yahooFinance.quote(ticker);
      const currentPrice = quote?.regularMarketPrice;
      const duration = Date.now() - startTime;

      if (!currentPrice) {
        throw new Error(`Current price not available for ${ticker}`);
      }

      logger.info('Successfully fetched current price', {
        component: 'DataService',
        operation,
        ticker,
        duration,
        currentPrice,
        marketState: quote?.marketState,
        lastUpdated: quote?.regularMarketTime
      });


      return currentPrice;
    } catch (error) {
      const duration = Date.now() - startTime;
      logger.warn('Failed to fetch current price, falling back to historical data', {
        component: 'DataService',
        operation,
        ticker,
        duration,
        error: (error as Error).message
      });

      // Fallback to latest close price
      const historicalData = await this.fetchHistoricalData(ticker);
      const fallbackPrice = historicalData.length > 0 ? historicalData[historicalData.length - 1].close : 0;

      logger.info('Using fallback price from historical data', {
        component: 'DataService',
        operation: `${operation}-fallback`,
        ticker,
        fallbackPrice,
        historicalRecords: historicalData.length
      });

      return fallbackPrice;
    }
  }

  /**
   * Fetch OHLCV data for multiple tickers
   */
  public async fetchMultipleHistoricalData(tickers: string[]): Promise<{ [key: string]: OHLCV[] }> {
    const data: { [key: string]: OHLCV[] } = {};

    const promises = tickers.map(ticker => this.fetchHistoricalData(ticker));
    const results = await Promise.allSettled(promises);

    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        data[tickers[index]] = result.value;
      } else {
        data[tickers[index]] = [];
      }
    });

    return data;
  }
} 